'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Device, HealthResponse } from '@/types/sms-gateway';
import {
  Smartphone,
  Send,
  Activity,
  MessageCircle,
  Webhook,
  Settings,
  ArrowRight,
  CheckCircle,
  XCircle,
  AlertCircle,
  Bot
} from 'lucide-react';

export function DashboardOverview() {
  const [devices, setDevices] = useState<Device[]>([]);
  const [health, setHealth] = useState<HealthResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [devicesResponse, healthResponse] = await Promise.all([
        fetch('/api/devices'),
        fetch('/api/health').catch(() => null) // Health endpoint might not be available in demo
      ]);

      if (devicesResponse.ok) {
        const devicesData = await devicesResponse.json();
        setDevices(devicesData);
      }

      if (healthResponse && healthResponse.ok) {
        const healthData = await healthResponse.json();
        setHealth(healthData);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getOnlineDevicesCount = () => {
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    return devices.filter(device => 
      new Date(device.lastSeen).getTime() > fiveMinutesAgo
    ).length;
  };

  const quickActions = [
    {
      title: 'Send Message',
      description: 'Send SMS messages through your gateway',
      href: '/messages',
      icon: Send,
      color: 'bg-blue-500',
    },
    {
      title: 'Track Status',
      description: 'Monitor message delivery status',
      href: '/status',
      icon: Activity,
      color: 'bg-green-500',
    },
    {
      title: 'View Inbox',
      description: 'Check received messages',
      href: '/inbox',
      icon: MessageCircle,
      color: 'bg-purple-500',
    },
    {
      title: 'Manage Webhooks',
      description: 'Configure real-time notifications',
      href: '/webhooks',
      icon: Webhook,
      color: 'bg-orange-500',
    },
    {
      title: 'LINE Bot',
      description: 'Configure LINE notifications',
      href: '/line',
      icon: Bot,
      color: 'bg-green-500',
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h1 className="text-3xl font-bold">Welcome to SMS Gateway</h1>
        <p className="text-muted-foreground mt-2">
          Manage your Android SMS Gateway integration from this dashboard
        </p>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connected Devices</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{devices.length}</div>
            <p className="text-xs text-muted-foreground">
              {getOnlineDevicesCount()} online
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            {health?.status === 'UP' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : health?.status === 'DOWN' ? (
              <XCircle className="h-4 w-4 text-red-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-yellow-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {health?.status || 'Unknown'}
            </div>
            <p className="text-xs text-muted-foreground">
              {health?.version ? `v${health.version}` : 'Status unavailable'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Messages Today</CardTitle>
            <Send className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              Feature coming soon
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              Feature coming soon
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <Link key={action.title} href={action.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{action.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {action.description}
                      </p>
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>

      {/* Device Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Device Status</CardTitle>
              <CardDescription>Overview of your connected SMS gateway devices</CardDescription>
            </div>
            <Link href="/settings">
              <Button variant="outline" size="sm">
                Manage Devices
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {devices.length === 0 ? (
            <div className="text-center py-8">
              <Smartphone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No devices connected</h3>
              <p className="text-muted-foreground mb-4">
                Connect your Android device to start sending SMS messages
              </p>
              <Link href="/settings">
                <Button>
                  <Settings className="h-4 w-4 mr-2" />
                  Go to Settings
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {devices.slice(0, 3).map((device) => {
                const isOnline = new Date(device.lastSeen).getTime() > Date.now() - 5 * 60 * 1000;
                return (
                  <div key={device.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
                      <div>
                        <h4 className="font-medium">{device.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          Last seen: {new Date(device.lastSeen).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {isOnline ? 'Online' : 'Offline'}
                    </span>
                  </div>
                );
              })}
              {devices.length > 3 && (
                <div className="text-center pt-2">
                  <Link href="/settings">
                    <Button variant="outline" size="sm">
                      View all {devices.length} devices
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
