import Client from 'android-sms-gateway';
import { FetchHttpClient } from './http-client';
import {
  MessageRequest,
  MessageState,
  WebHook,
  RegisterWebHookRequest,
  Device,
  DeviceSettings,
  HealthResponse,
  LogEntry,
  MessagesExportRequest,
} from '@/types/sms-gateway';

export class SmsGatewayService {
  private client: Client;

  constructor(
    login: string,
    password: string,
    baseUrl: string = 'https://api.sms-gate.app/3rdparty/v1'
  ) {
    const httpClient = new FetchHttpClient();
    this.client = new Client(login, password, httpClient, baseUrl);
  }

  // Message operations
  async sendMessage(
    message: MessageRequest,
    options?: { skipPhoneValidation?: boolean }
  ): Promise<MessageState> {
    return this.client.send(message, options);
  }

  async getMessageState(messageId: string): Promise<MessageState> {
    return this.client.getState(messageId);
  }

  // Webhook operations
  async getWebhooks(): Promise<WebHook[]> {
    return this.client.getWebhooks();
  }

  async registerWebhook(request: RegisterWebHookRequest): Promise<WebHook> {
    return this.client.registerWebhook(request);
  }

  async deleteWebhook(webhookId: string): Promise<void> {
    return this.client.deleteWebhook(webhookId);
  }

  // Device operations
  async getDevices(): Promise<Device[]> {
    return this.client.getDevices();
  }

  async deleteDevice(deviceId: string): Promise<void> {
    return this.client.deleteDevice(deviceId);
  }

  // Health check
  async getHealth(): Promise<HealthResponse> {
    return this.client.getHealth();
  }

  // Inbox export
  async exportInbox(request: MessagesExportRequest): Promise<void> {
    return this.client.exportInbox(request);
  }

  // Logs
  async getLogs(from?: Date, to?: Date): Promise<LogEntry[]> {
    return this.client.getLogs(from, to);
  }

  // Settings operations
  async getSettings(): Promise<DeviceSettings> {
    return this.client.getSettings();
  }

  async updateSettings(settings: DeviceSettings): Promise<void> {
    return this.client.updateSettings(settings);
  }

  async patchSettings(settings: Partial<DeviceSettings>): Promise<void> {
    return this.client.patchSettings(settings);
  }
}

// Singleton instance
let smsGatewayService: SmsGatewayService | null = null;

export function getSmsGatewayService(): SmsGatewayService {
  if (!smsGatewayService) {
    const login = process.env.ANDROID_SMS_GATEWAY_LOGIN;
    const password = process.env.ANDROID_SMS_GATEWAY_PASSWORD;
    const baseUrl = process.env.ANDROID_SMS_GATEWAY_BASE_URL;

    if (!login || !password) {
      throw new Error('SMS Gateway credentials not configured');
    }

    smsGatewayService = new SmsGatewayService(login, password, baseUrl);
  }

  return smsGatewayService;
}
