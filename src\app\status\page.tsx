'use client';

import { MessageTracker } from '@/components/status/message-tracker';
import { StatusDashboard } from '@/components/status/status-dashboard';

export default function StatusPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Status Tracking</h1>
        <p className="text-muted-foreground mt-2">
          Monitor message delivery status and track individual messages
        </p>
      </div>

      <div className="space-y-8">
        <StatusDashboard />
        <MessageTracker />
      </div>
    </div>
  );
}
