import { NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';

export async function GET() {
  try {
    const smsService = getSmsGatewayService();
    const devices = await smsService.getDevices();
    
    return NextResponse.json(devices);
  } catch (error) {
    console.error('Error fetching devices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch devices' },
      { status: 500 }
    );
  }
}
