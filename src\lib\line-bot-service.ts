import { Client, Message, TextMessage } from '@line/bot-sdk';

export interface LineConfig {
  channelAccessToken: string;
  channelSecret: string;
}

export interface SmsForwardMessage {
  from: string;
  message: string;
  receivedAt: string;
  deviceId: string;
  deviceName?: string;
}

export class LineBotService {
  private client: Client;

  constructor(config: LineConfig) {
    this.client = new Client(config);
  }

  /**
   * Send a text message to a specific LINE user
   */
  async sendTextMessage(userId: string, text: string): Promise<void> {
    const message: TextMessage = {
      type: 'text',
      text: text
    };

    await this.client.pushMessage(userId, message);
  }

  /**
   * Send multiple messages to a specific LINE user
   */
  async sendMessages(userId: string, messages: Message[]): Promise<void> {
    await this.client.pushMessage(userId, messages);
  }

  /**
   * Format and send SMS notification to LINE
   */
  async forwardSmsToLine(userId: string, smsData: SmsForwardMessage): Promise<void> {
    const formattedMessage = this.formatSmsMessage(smsData);
    await this.sendTextMessage(userId, formattedMessage);
  }

  /**
   * Format SMS data into a readable LINE message
   */
  private formatSmsMessage(smsData: SmsForwardMessage): string {
    const timestamp = new Date(smsData.receivedAt).toLocaleString();
    const deviceInfo = smsData.deviceName ? `${smsData.deviceName} (${smsData.deviceId})` : smsData.deviceId;
    
    return `📱 SMS Received

From: ${smsData.from}
Device: ${deviceInfo}
Time: ${timestamp}

Message:
${smsData.message}`;
  }

  /**
   * Send a test message to verify LINE Bot is working
   */
  async sendTestMessage(userId: string): Promise<void> {
    const testMessage = `🤖 LINE Bot Test

This is a test message from your SMS Gateway application.
The LINE Bot integration is working correctly!

Time: ${new Date().toLocaleString()}`;

    await this.sendTextMessage(userId, testMessage);
  }

  /**
   * Send SMS statistics summary to LINE
   */
  async sendSmsStatistics(userId: string, stats: {
    totalReceived: number;
    todayReceived: number;
    lastMessageTime?: string;
  }): Promise<void> {
    const message = `📊 SMS Statistics

Total Messages: ${stats.totalReceived}
Today: ${stats.todayReceived}
${stats.lastMessageTime ? `Last Message: ${new Date(stats.lastMessageTime).toLocaleString()}` : 'No messages yet'}

Generated: ${new Date().toLocaleString()}`;

    await this.sendTextMessage(userId, message);
  }
}

// Singleton instance
let lineBotService: LineBotService | null = null;

export function getLineBotService(): LineBotService {
  if (!lineBotService) {
    const channelAccessToken = process.env.LINE_CHANNEL_ACCESS_TOKEN;
    const channelSecret = process.env.LINE_CHANNEL_SECRET;

    if (!channelAccessToken || !channelSecret) {
      throw new Error('LINE Bot credentials not configured');
    }

    lineBotService = new LineBotService({
      channelAccessToken,
      channelSecret
    });
  }

  return lineBotService;
}

export function getLineUserId(): string {
  const userId = process.env.LINE_USER_ID;
  if (!userId) {
    throw new Error('LINE_USER_ID not configured');
  }
  return userId;
}
