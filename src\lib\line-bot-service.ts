import { Client, Message, TextMessage, QuickReply, QuickReplyItem } from '@line/bot-sdk';

export interface LineConfig {
  channelAccessToken: string;
  channelSecret: string;
}

export interface SmsForwardMessage {
  from: string;
  message: string;
  receivedAt: string;
  deviceId: string;
  deviceName?: string;
}

export class LineBotService {
  private client: Client;
  private authorizedNumbers: Set<string>;

  constructor(config: LineConfig) {
    this.client = new Client(config);

    // Load authorized numbers from environment
    const authorizedNumbersEnv = process.env.LINE_AUTHORIZED_NUMBERS || '';
    this.authorizedNumbers = new Set(
      authorizedNumbersEnv.split(',').map(num => num.trim()).filter(num => num.length > 0)
    );
  }

  /**
   * Check if a phone number is authorized to use the bot
   */
  isAuthorizedNumber(phoneNumber: string): boolean {
    return this.authorizedNumbers.has(phoneNumber);
  }

  /**
   * Get list of authorized numbers (for admin purposes)
   */
  getAuthorizedNumbers(): string[] {
    return Array.from(this.authorizedNumbers);
  }

  /**
   * Send a text message to a specific LINE user
   */
  async sendTextMessage(userId: string, text: string, quickReply?: QuickReply): Promise<void> {
    const message: TextMessage = {
      type: 'text',
      text: text,
      ...(quickReply && { quickReply })
    };

    await this.client.pushMessage(userId, message);
  }

  /**
   * Create quick reply buttons for common actions
   */
  createQuickReplyButtons(): QuickReply {
    const items: QuickReplyItem[] = [
      {
        type: 'action',
        action: {
          type: 'message',
          label: '📱 Devices',
          text: '/devices'
        }
      },
      {
        type: 'action',
        action: {
          type: 'message',
          label: '📤 Send SMS',
          text: '/send'
        }
      },
      {
        type: 'action',
        action: {
          type: 'message',
          label: '📊 Statistics',
          text: '/stats'
        }
      },
      {
        type: 'action',
        action: {
          type: 'message',
          label: '📥 Inbox',
          text: '/inbox'
        }
      },
      {
        type: 'action',
        action: {
          type: 'message',
          label: '🔗 Webhooks',
          text: '/webhooks'
        }
      },
      {
        type: 'action',
        action: {
          type: 'message',
          label: '❓ Help',
          text: '/help'
        }
      }
    ];

    return { items };
  }

  /**
   * Send multiple messages to a specific LINE user
   */
  async sendMessages(userId: string, messages: Message[]): Promise<void> {
    await this.client.pushMessage(userId, messages);
  }

  /**
   * Format and send SMS notification to LINE (with authorization check)
   */
  async forwardSmsToLine(userId: string, smsData: SmsForwardMessage): Promise<void> {
    // Check if the sender is authorized
    if (!this.isAuthorizedNumber(smsData.from)) {
      console.log(`📵 SMS from unauthorized number ${smsData.from} - not forwarding to LINE`);
      return;
    }

    const formattedMessage = this.formatSmsMessage(smsData);
    const quickReply = this.createQuickReplyButtons();

    await this.sendTextMessage(userId, formattedMessage, quickReply);
  }

  /**
   * Format SMS data into a readable LINE message
   */
  private formatSmsMessage(smsData: SmsForwardMessage): string {
    const timestamp = new Date(smsData.receivedAt).toLocaleString();
    const deviceInfo = smsData.deviceName ? `${smsData.deviceName} (${smsData.deviceId})` : smsData.deviceId;
    
    return `📱 SMS Received

From: ${smsData.from}
Device: ${deviceInfo}
Time: ${timestamp}

Message:
${smsData.message}`;
  }

  /**
   * Send a test message to verify LINE Bot is working
   */
  async sendTestMessage(userId: string): Promise<void> {
    const testMessage = `🤖 LINE Bot Test

This is a test message from your SMS Gateway application.
The LINE Bot integration is working correctly!

Time: ${new Date().toLocaleString()}`;

    const quickReply = this.createQuickReplyButtons();
    await this.sendTextMessage(userId, testMessage, quickReply);
  }

  /**
   * Send welcome message with quick reply buttons
   */
  async sendWelcomeMessage(userId: string): Promise<void> {
    const welcomeMessage = `🎉 Welcome to SMS Gateway Bot!

I can help you manage your SMS gateway. Here are the available commands:

📱 /devices - View connected devices
📤 /send - Send SMS message
📊 /stats - View statistics
📥 /inbox - Check received messages
🔗 /webhooks - Manage webhooks
❓ /help - Show this help message

Use the quick buttons below or type commands directly!`;

    const quickReply = this.createQuickReplyButtons();
    await this.sendTextMessage(userId, welcomeMessage, quickReply);
  }

  /**
   * Send help message
   */
  async sendHelpMessage(userId: string): Promise<void> {
    const helpMessage = `❓ SMS Gateway Bot Help

Available Commands:
• /devices - List all connected devices
• /send - Send SMS message (interactive)
• /stats - Show SMS statistics
• /inbox - Show recent received messages
• /webhooks - List active webhooks
• /help - Show this help message

🔒 Security: Only authorized phone numbers can trigger SMS forwarding.

📱 Authorized Numbers: ${this.getAuthorizedNumbers().join(', ')}`;

    const quickReply = this.createQuickReplyButtons();
    await this.sendTextMessage(userId, helpMessage, quickReply);
  }

  /**
   * Send SMS statistics summary to LINE
   */
  async sendSmsStatistics(userId: string, stats: {
    totalReceived: number;
    todayReceived: number;
    lastMessageTime?: string;
  }): Promise<void> {
    const message = `📊 SMS Statistics

Total Messages: ${stats.totalReceived}
Today: ${stats.todayReceived}
${stats.lastMessageTime ? `Last Message: ${new Date(stats.lastMessageTime).toLocaleString()}` : 'No messages yet'}

Generated: ${new Date().toLocaleString()}`;

    await this.sendTextMessage(userId, message);
  }
}

// Singleton instance
let lineBotService: LineBotService | null = null;

export function getLineBotService(): LineBotService {
  if (!lineBotService) {
    const channelAccessToken = process.env.LINE_CHANNEL_ACCESS_TOKEN;
    const channelSecret = process.env.LINE_CHANNEL_SECRET;

    if (!channelAccessToken || !channelSecret) {
      throw new Error('LINE Bot credentials not configured');
    }

    lineBotService = new LineBotService({
      channelAccessToken,
      channelSecret
    });
  }

  return lineBotService;
}

export function getLineUserId(): string {
  const userId = process.env.LINE_USER_ID;
  if (!userId) {
    throw new Error('LINE_USER_ID not configured');
  }
  return userId;
}
