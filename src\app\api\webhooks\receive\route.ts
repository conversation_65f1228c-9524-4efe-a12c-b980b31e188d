import { NextRequest, NextResponse } from 'next/server';
import { WebhookPayload, WebHookEventType } from '@/types/sms-gateway';
import crypto from 'crypto';

// This endpoint receives webhook notifications from the SMS Gateway
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const payload: WebhookPayload = JSON.parse(body);
    
    // Verify webhook signature if secret is configured
    const webhookSecret = process.env.WEBHOOK_SECRET;
    if (webhookSecret) {
      const signature = request.headers.get('x-signature');
      if (!signature || !verifySignature(body, signature, webhookSecret)) {
        return NextResponse.json(
          { error: 'Invalid signature' },
          { status: 401 }
        );
      }
    }

    // Process the webhook payload
    await processWebhookPayload(payload);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}

function verifySignature(body: string, signature: string, secret: string): boolean {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(body)
      .digest('hex');
    
    return signature === `sha256=${expectedSignature}`;
  } catch (error) {
    console.error('Error verifying signature:', error);
    return false;
  }
}

async function processWebhookPayload(payload: WebhookPayload) {
  console.log('Processing webhook payload:', payload);
  
  switch (payload.event) {
    case WebHookEventType.SmsReceived:
      await handleSmsReceived(payload);
      break;
      
    case WebHookEventType.SmsStatusChanged:
      await handleSmsStatusChanged(payload);
      break;
      
    default:
      console.warn('Unknown webhook event type:', payload.event);
  }
}

async function handleSmsReceived(payload: any) {
  console.log('SMS received:', payload.message);
  
  // Store the received message (in a real app, you'd save to database)
  // For now, we'll use a simple approach to demonstrate
  try {
    // You could save to database here
    // await saveReceivedMessage(payload.message);
    
    // For demo purposes, we'll store in a way that can be accessed by the frontend
    // In production, you'd use a proper database and real-time updates
    console.log('Received SMS from:', payload.message.phoneNumber);
    console.log('Message text:', payload.message.text);
    console.log('Device ID:', payload.deviceId);
  } catch (error) {
    console.error('Error handling received SMS:', error);
  }
}

async function handleSmsStatusChanged(payload: any) {
  console.log('SMS status changed:', payload.message);
  
  try {
    // Update message status in database
    // await updateMessageStatus(payload.message.id, payload.message.state);
    
    console.log('Message status updated:', payload.message.id, payload.message.state);
  } catch (error) {
    console.error('Error handling SMS status change:', error);
  }
}
