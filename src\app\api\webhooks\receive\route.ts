import { NextRequest, NextResponse } from 'next/server';
import { WebhookPayload, WebHookEventType } from '@/types/sms-gateway';
import { getLineBotService, getLineUserId } from '@/lib/line-bot-service';
import crypto from 'crypto';

// This endpoint receives webhook notifications from the SMS Gateway
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();

    // Log headers for debugging
    console.log('Webhook headers:', Object.fromEntries(request.headers.entries()));
    console.log('Webhook body:', body);

    const payload: WebhookPayload = JSON.parse(body);

    // Verify webhook signature if secret is configured
    const webhookSecret = process.env.WEBHOOK_SECRET;
    const skipSignatureVerification = process.env.NODE_ENV === 'development' && process.env.SKIP_WEBHOOK_SIGNATURE === 'true';

    if (webhookSecret && !skipSignatureVerification) {
      // Try different possible signature header names
      const signature = request.headers.get('x-signature') ||
                       request.headers.get('x-hub-signature-256') ||
                       request.headers.get('signature');

      if (!signature) {
        console.log('No signature header found. Available headers:', Object.keys(Object.fromEntries(request.headers.entries())));
        return NextResponse.json(
          { error: 'No signature provided' },
          { status: 401 }
        );
      }

      if (!verifySignature(body, signature, webhookSecret)) {
        console.log('Signature verification failed');
        console.log('Received signature:', signature);
        console.log('Expected signature format: sha256=<hash>');
        return NextResponse.json(
          { error: 'Invalid signature' },
          { status: 401 }
        );
      }
    } else if (skipSignatureVerification) {
      console.log('⚠️  Webhook signature verification skipped (development mode)');
    }

    // Process the webhook payload
    await processWebhookPayload(payload);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}

function verifySignature(body: string, signature: string, secret: string): boolean {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(body)
      .digest('hex');

    // Try different signature formats
    const possibleFormats = [
      `sha256=${expectedSignature}`,
      expectedSignature,
      `sha256:${expectedSignature}`,
    ];

    console.log('Verifying signature...');
    console.log('Received signature:', signature);
    console.log('Expected signatures:', possibleFormats);

    // Use timing-safe comparison
    for (const format of possibleFormats) {
      if (crypto.timingSafeEqual(
        Buffer.from(signature, 'utf8'),
        Buffer.from(format, 'utf8')
      )) {
        console.log('Signature verified successfully');
        return true;
      }
    }

    console.log('Signature verification failed - no format matched');
    return false;
  } catch (error) {
    console.error('Error verifying signature:', error);
    return false;
  }
}

async function processWebhookPayload(payload: WebhookPayload) {
  console.log('Processing webhook payload:', payload);

  switch (payload.event) {
    case WebHookEventType.SmsReceived:
      await handleSmsReceived(payload);
      break;

    case WebHookEventType.SmsSent:
      await handleSmsSent(payload);
      break;

    case WebHookEventType.SmsDelivered:
      await handleSmsDelivered(payload);
      break;

    case WebHookEventType.SmsFailed:
      await handleSmsFailed(payload);
      break;

    default:
      console.warn('Unknown webhook event type:', (payload as any).event);
  }
}

async function handleSmsReceived(payload: any) {
  console.log('SMS received:', payload.message);

  try {
    // Store the received message (in a real app, you'd save to database)
    console.log('Received SMS from:', payload.message.phoneNumber);
    console.log('Message text:', payload.message.text);
    console.log('Device ID:', payload.deviceId);

    // Forward SMS to LINE Bot
    try {
      const lineBotService = getLineBotService();
      const lineUserId = getLineUserId();

      await lineBotService.forwardSmsToLine(lineUserId, {
        from: payload.message.phoneNumber,
        message: payload.message.text,
        receivedAt: payload.message.receivedAt,
        deviceId: payload.deviceId,
        deviceName: payload.deviceName || undefined
      });

      console.log('✅ SMS forwarded to LINE successfully');
    } catch (lineError) {
      console.error('❌ Error forwarding SMS to LINE:', lineError);
      // Don't throw here - we don't want to fail the webhook if LINE forwarding fails
    }

    // Store message in localStorage for demo (in production, use database)
    // This is a simple way to make received messages available to the frontend

  } catch (error) {
    console.error('Error handling received SMS:', error);
  }
}

async function handleSmsSent(payload: any) {
  console.log('SMS sent:', payload);

  try {
    // Handle SMS sent event
    console.log('SMS sent for device:', payload.deviceId);
  } catch (error) {
    console.error('Error handling SMS sent:', error);
  }
}

async function handleSmsDelivered(payload: any) {
  console.log('SMS delivered:', payload);

  try {
    // Handle SMS delivered event
    console.log('SMS delivered for device:', payload.deviceId);
  } catch (error) {
    console.error('Error handling SMS delivered:', error);
  }
}

async function handleSmsFailed(payload: any) {
  console.log('SMS failed:', payload);

  try {
    // Handle SMS failed event
    console.log('SMS failed for device:', payload.deviceId);
  } catch (error) {
    console.error('Error handling SMS failed:', error);
  }
}
