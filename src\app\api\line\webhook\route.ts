import { NextRequest, NextResponse } from 'next/server';
import { WebhookEvent, MessageEvent, TextEventMessage } from '@line/bot-sdk';
import { getLineBotService, getLineUserId } from '@/lib/line-bot-service';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-line-signature');
    
    // Verify LINE webhook signature
    if (!verifyLineSignature(body, signature)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const events: WebhookEvent[] = JSON.parse(body).events;
    
    for (const event of events) {
      await handleLineEvent(event);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing LINE webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process LINE webhook' },
      { status: 500 }
    );
  }
}

function verifyLineSignature(body: string, signature: string | null): boolean {
  if (!signature) return false;
  
  const channelSecret = process.env.LINE_CHANNEL_SECRET;
  if (!channelSecret) return false;
  
  const expectedSignature = crypto
    .createHmac('sha256', channelSecret)
    .update(body)
    .digest('base64');
  
  return signature === expectedSignature;
}

async function handleLineEvent(event: WebhookEvent) {
  if (event.type !== 'message' || event.message.type !== 'text') {
    return;
  }

  const messageEvent = event as MessageEvent;
  const textMessage = messageEvent.message as TextEventMessage;
  const userId = messageEvent.source.userId;
  
  if (!userId) return;

  const lineBotService = getLineBotService();
  const text = textMessage.text.trim();

  try {
    // Handle commands
    switch (text.toLowerCase()) {
      case '/start':
      case '/help':
        await lineBotService.sendHelpMessage(userId);
        break;
        
      case '/devices':
        await handleDevicesCommand(userId);
        break;
        
      case '/stats':
        await handleStatsCommand(userId);
        break;
        
      case '/webhooks':
        await handleWebhooksCommand(userId);
        break;
        
      case '/inbox':
        await handleInboxCommand(userId);
        break;
        
      case '/send':
        await handleSendCommand(userId);
        break;
        
      default:
        // If not a command, send help message
        await lineBotService.sendHelpMessage(userId);
        break;
    }
  } catch (error) {
    console.error('Error handling LINE command:', error);
    await lineBotService.sendTextMessage(userId, '❌ Sorry, an error occurred while processing your request.');
  }
}

async function handleDevicesCommand(userId: string) {
  try {
    const smsService = getSmsGatewayService();
    const devices = await smsService.getDevices();
    
    if (devices.length === 0) {
      await getLineBotService().sendTextMessage(userId, '📱 No devices connected');
      return;
    }
    
    let message = '📱 Connected Devices:\n\n';
    devices.forEach((device, index) => {
      const isOnline = new Date(device.lastSeen).getTime() > Date.now() - 5 * 60 * 1000;
      const status = isOnline ? '🟢 Online' : '🔴 Offline';
      
      message += `${index + 1}. ${device.name}\n`;
      message += `   ID: ${device.id}\n`;
      message += `   Status: ${status}\n`;
      message += `   Last seen: ${new Date(device.lastSeen).toLocaleString()}\n\n`;
    });
    
    const lineBotService = getLineBotService();
    const quickReply = lineBotService.createQuickReplyButtons();
    await lineBotService.sendTextMessage(userId, message, quickReply);
  } catch (error) {
    await getLineBotService().sendTextMessage(userId, '❌ Failed to fetch devices');
  }
}

async function handleStatsCommand(userId: string) {
  try {
    // In a real app, you'd fetch actual statistics from your database
    const stats = {
      totalReceived: Math.floor(Math.random() * 100) + 50,
      todayReceived: Math.floor(Math.random() * 20) + 5,
      lastMessageTime: new Date().toISOString()
    };
    
    await getLineBotService().sendSmsStatistics(userId, stats);
  } catch (error) {
    await getLineBotService().sendTextMessage(userId, '❌ Failed to fetch statistics');
  }
}

async function handleWebhooksCommand(userId: string) {
  try {
    const smsService = getSmsGatewayService();
    const webhooks = await smsService.getWebhooks();
    
    if (webhooks.length === 0) {
      await getLineBotService().sendTextMessage(userId, '🔗 No webhooks configured');
      return;
    }
    
    let message = '🔗 Active Webhooks:\n\n';
    webhooks.forEach((webhook, index) => {
      message += `${index + 1}. ${webhook.event}\n`;
      message += `   URL: ${webhook.url}\n`;
      message += `   Device: ${webhook.deviceId || 'All devices'}\n\n`;
    });
    
    const lineBotService = getLineBotService();
    const quickReply = lineBotService.createQuickReplyButtons();
    await lineBotService.sendTextMessage(userId, message, quickReply);
  } catch (error) {
    await getLineBotService().sendTextMessage(userId, '❌ Failed to fetch webhooks');
  }
}

async function handleInboxCommand(userId: string) {
  const message = `📥 Inbox Feature

This feature shows recent received SMS messages.
In a production app, this would display:

• Recent received messages
• Sender information
• Message timestamps
• Device information

Currently showing demo data as the inbox is stored locally in the web app.`;

  const lineBotService = getLineBotService();
  const quickReply = lineBotService.createQuickReplyButtons();
  await lineBotService.sendTextMessage(userId, message, quickReply);
}

async function handleSendCommand(userId: string) {
  const message = `📤 Send SMS Feature

To send SMS messages, please use the web interface at:
${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/messages

The web interface provides:
• Phone number validation
• Message composition
• Device selection
• Delivery tracking
• Bulk messaging

This ensures proper formatting and validation of your SMS messages.`;

  const lineBotService = getLineBotService();
  const quickReply = lineBotService.createQuickReplyButtons();
  await lineBotService.sendTextMessage(userId, message, quickReply);
}
