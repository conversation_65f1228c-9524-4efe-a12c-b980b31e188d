'use client';

import { InboxExportForm } from '@/components/inbox/inbox-export-form';
import { InboxViewer } from '@/components/inbox/inbox-viewer';

export default function InboxPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Message Inbox</h1>
        <p className="text-muted-foreground mt-2">
          View and manage received SMS messages from your gateway devices
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <InboxExportForm />
        </div>
        
        <div className="lg:col-span-2">
          <InboxViewer />
        </div>
      </div>
    </div>
  );
}
