import { NextResponse } from 'next/server';
import { getLineBotService, getLineUserId } from '@/lib/line-bot-service';

export async function POST() {
  try {
    const lineBotService = getLineBotService();
    const lineUserId = getLineUserId();
    
    await lineBotService.sendTestMessage(lineUserId);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Test message sent to LINE successfully' 
    });
  } catch (error) {
    console.error('Error sending LINE test message:', error);
    return NextResponse.json(
      { 
        error: 'Failed to send test message to LINE',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
