import { NextRequest, NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';

export async function GET() {
  try {
    const smsService = getSmsGatewayService();
    const settings = await smsService.getSettings();
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const settings = await request.json();
    const smsService = getSmsGatewayService();
    
    await smsService.updateSettings(settings);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const partialSettings = await request.json();
    const smsService = getSmsGatewayService();
    
    await smsService.patchSettings(partialSettings);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error patching settings:', error);
    return NextResponse.json(
      { error: 'Failed to patch settings' },
      { status: 500 }
    );
  }
}
