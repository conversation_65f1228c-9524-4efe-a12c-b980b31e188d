import { NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';

export async function GET() {
  try {
    const smsService = getSmsGatewayService();
    const health = await smsService.getHealth();
    
    return NextResponse.json(health);
  } catch (error) {
    console.error('Error fetching health:', error);
    return NextResponse.json(
      { error: 'Failed to fetch health status' },
      { status: 500 }
    );
  }
}
