import { NextRequest, NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { webhookId: string } }
) {
  try {
    const { webhookId } = params;
    const smsService = getSmsGatewayService();
    
    await smsService.deleteWebhook(webhookId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting webhook:', error);
    return NextResponse.json(
      { error: 'Failed to delete webhook' },
      { status: 500 }
    );
  }
}
