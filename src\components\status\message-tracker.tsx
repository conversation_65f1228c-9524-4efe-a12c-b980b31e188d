'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageStatus } from '@/components/messages/message-status';
import { MessageState } from '@/types/sms-gateway';
import { Search } from 'lucide-react';

export function MessageTracker() {
  const [messageId, setMessageId] = useState('');
  const [messageState, setMessageState] = useState<MessageState | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageId.trim()) {
      setError('Please enter a message ID');
      return;
    }

    setLoading(true);
    setError(null);
    setMessageState(null);

    try {
      const response = await fetch(`/api/messages/${messageId.trim()}`);
      
      if (response.ok) {
        const data = await response.json();
        setMessageState(data);
      } else if (response.status === 404) {
        setError('Message not found');
      } else {
        setError('Failed to fetch message status');
      }
    } catch (error) {
      console.error('Error fetching message status:', error);
      setError('Failed to fetch message status');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    if (!messageState) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/messages/${messageState.id}`);
      if (response.ok) {
        const data = await response.json();
        setMessageState(data);
      }
    } catch (error) {
      console.error('Error refreshing message status:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Track Message</span>
          </CardTitle>
          <CardDescription>
            Enter a message ID to track its delivery status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex space-x-2">
            <Input
              type="text"
              value={messageId}
              onChange={(e) => setMessageId(e.target.value)}
              placeholder="Enter message ID..."
              className="flex-1"
            />
            <Button type="submit" disabled={loading}>
              {loading ? 'Searching...' : 'Track'}
            </Button>
          </form>
          
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {messageState && (
        <MessageStatus
          messageState={messageState}
          onRefresh={handleRefresh}
        />
      )}
    </div>
  );
}
