'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageState, ProcessState } from '@/types/sms-gateway';
import { getStatusColor, formatDateTime } from '@/lib/utils';
import { RefreshCw, CheckCircle, XCircle, Clock, Send } from 'lucide-react';

interface MessageStatusProps {
  messageState: MessageState;
  onRefresh?: () => void;
}

export function MessageStatus({ messageState, onRefresh }: MessageStatusProps) {
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (!onRefresh) return;
    
    setRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusIcon = (status: ProcessState) => {
    switch (status) {
      case ProcessState.Pending:
        return <Clock className="h-4 w-4" />;
      case ProcessState.Processed:
        return <Send className="h-4 w-4" />;
      case ProcessState.Sent:
        return <CheckCircle className="h-4 w-4" />;
      case ProcessState.Delivered:
        return <CheckCircle className="h-4 w-4" />;
      case ProcessState.Failed:
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getOverallStatus = () => {
    const statuses = messageState.recipients.map(r => r.state);
    
    if (statuses.every(s => s === ProcessState.Delivered)) {
      return { status: ProcessState.Delivered, color: 'text-green-700 bg-green-200' };
    }
    
    if (statuses.some(s => s === ProcessState.Failed)) {
      return { status: ProcessState.Failed, color: 'text-red-600 bg-red-100' };
    }
    
    if (statuses.some(s => s === ProcessState.Sent)) {
      return { status: ProcessState.Sent, color: 'text-green-600 bg-green-100' };
    }
    
    if (statuses.some(s => s === ProcessState.Processed)) {
      return { status: ProcessState.Processed, color: 'text-blue-600 bg-blue-100' };
    }
    
    return { status: ProcessState.Pending, color: 'text-yellow-600 bg-yellow-100' };
  };

  const overallStatus = getOverallStatus();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              {getStatusIcon(overallStatus.status)}
              <span>Message Status</span>
            </CardTitle>
            <CardDescription>
              Message ID: {messageState.id}
            </CardDescription>
          </div>
          
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Overall Status */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Overall Status:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${overallStatus.color}`}>
            {overallStatus.status}
          </span>
        </div>

        {/* Recipients Status */}
        <div>
          <h4 className="text-sm font-medium mb-3">Recipients ({messageState.recipients.length})</h4>
          <div className="space-y-2">
            {messageState.recipients.map((recipient, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  {getStatusIcon(recipient.state)}
                  <div>
                    <div className="font-medium">{recipient.phoneNumber}</div>
                    {recipient.error && (
                      <div className="text-sm text-red-600 mt-1">
                        Error: {recipient.error}
                      </div>
                    )}
                  </div>
                </div>
                
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(recipient.state)}`}>
                  {recipient.state}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Status Summary */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 pt-4 border-t">
          {Object.values(ProcessState).map(status => {
            const count = messageState.recipients.filter(r => r.state === status).length;
            return (
              <div key={status} className="text-center">
                <div className="text-lg font-semibold">{count}</div>
                <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(status)}`}>
                  {status}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
