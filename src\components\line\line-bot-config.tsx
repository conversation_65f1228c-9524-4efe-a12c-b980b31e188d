'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageCircle, Send, BarChart3, CheckCircle, XCircle } from 'lucide-react';

export function LineBotConfig() {
  const [testingConnection, setTestingConnection] = useState(false);
  const [sendingStats, setSendingStats] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [statsResult, setStatsResult] = useState<{ success: boolean; message: string } | null>(null);

  const testLineConnection = async () => {
    setTestingConnection(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/line/test', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setTestResult({ success: true, message: data.message });
      } else {
        setTestResult({ success: false, message: data.error || 'Failed to send test message' });
      }
    } catch (error) {
      console.error('Error testing LINE connection:', error);
      setTestResult({ success: false, message: 'Network error occurred' });
    } finally {
      setTestingConnection(false);
    }
  };

  const sendStatsToLine = async () => {
    setSendingStats(true);
    setStatsResult(null);

    try {
      const response = await fetch('/api/line/stats', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setStatsResult({ success: true, message: data.message });
      } else {
        setStatsResult({ success: false, message: data.error || 'Failed to send statistics' });
      }
    } catch (error) {
      console.error('Error sending stats to LINE:', error);
      setStatsResult({ success: false, message: 'Network error occurred' });
    } finally {
      setSendingStats(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5 text-green-600" />
            <span>LINE Bot Integration</span>
          </CardTitle>
          <CardDescription>
            Configure and test your LINE Bot integration for SMS forwarding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Info */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Setup Instructions</h4>
            <ol className="text-xs text-blue-700 space-y-1 list-decimal list-inside">
              <li>Create a LINE Bot in the LINE Developers Console</li>
              <li>Get your Channel Access Token and Channel Secret</li>
              <li>Add your LINE User ID (you can get this from LINE Bot)</li>
              <li>Configure the environment variables in .env.local</li>
              <li>Test the connection using the button below</li>
            </ol>
          </div>

          {/* Environment Variables */}
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Required Environment Variables</h4>
            <div className="text-xs text-gray-700 space-y-1 font-mono">
              <div>LINE_CHANNEL_ACCESS_TOKEN=your_channel_access_token</div>
              <div>LINE_CHANNEL_SECRET=your_channel_secret</div>
              <div>LINE_USER_ID=your_user_id</div>
            </div>
          </div>

          {/* Test Connection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Test LINE Bot Connection</h4>
                <p className="text-xs text-muted-foreground">
                  Send a test message to verify your LINE Bot is working
                </p>
              </div>
              <Button
                onClick={testLineConnection}
                disabled={testingConnection}
                variant="outline"
              >
                <Send className="h-4 w-4 mr-2" />
                {testingConnection ? 'Testing...' : 'Test Connection'}
              </Button>
            </div>

            {testResult && (
              <div className={`flex items-center space-x-2 p-3 rounded-md ${
                testResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm ${
                  testResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {testResult.message}
                </span>
              </div>
            )}
          </div>

          {/* Send Statistics */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Send SMS Statistics</h4>
                <p className="text-xs text-muted-foreground">
                  Send current SMS statistics to your LINE account
                </p>
              </div>
              <Button
                onClick={sendStatsToLine}
                disabled={sendingStats}
                variant="outline"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                {sendingStats ? 'Sending...' : 'Send Stats'}
              </Button>
            </div>

            {statsResult && (
              <div className={`flex items-center space-x-2 p-3 rounded-md ${
                statsResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {statsResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm ${
                  statsResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {statsResult.message}
                </span>
              </div>
            )}
          </div>

          {/* Features */}
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <h4 className="text-sm font-medium text-green-900 mb-2">LINE Bot Features</h4>
            <ul className="text-xs text-green-700 space-y-1 list-disc list-inside">
              <li>Automatic SMS forwarding when messages are received</li>
              <li>Formatted messages with sender, device, and timestamp info</li>
              <li>SMS statistics reports on demand</li>
              <li>Test message functionality</li>
              <li>Error handling and fallback mechanisms</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
