'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageCircle, Send, BarChart3, CheckCircle, XCircle } from 'lucide-react';

export function LineBotConfig() {
  const [testingConnection, setTestingConnection] = useState(false);
  const [sendingStats, setSendingStats] = useState(false);
  const [sendingWelcome, setSendingWelcome] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [statsResult, setStatsResult] = useState<{ success: boolean; message: string } | null>(null);
  const [welcomeResult, setWelcomeResult] = useState<{ success: boolean; message: string } | null>(null);

  const testLineConnection = async () => {
    setTestingConnection(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/line/test', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setTestResult({ success: true, message: data.message });
      } else {
        setTestResult({ success: false, message: data.error || 'Failed to send test message' });
      }
    } catch (error) {
      console.error('Error testing LINE connection:', error);
      setTestResult({ success: false, message: 'Network error occurred' });
    } finally {
      setTestingConnection(false);
    }
  };

  const sendStatsToLine = async () => {
    setSendingStats(true);
    setStatsResult(null);

    try {
      const response = await fetch('/api/line/stats', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setStatsResult({ success: true, message: data.message });
      } else {
        setStatsResult({ success: false, message: data.error || 'Failed to send statistics' });
      }
    } catch (error) {
      console.error('Error sending stats to LINE:', error);
      setStatsResult({ success: false, message: 'Network error occurred' });
    } finally {
      setSendingStats(false);
    }
  };

  const sendWelcomeMessage = async () => {
    setSendingWelcome(true);
    setWelcomeResult(null);

    try {
      const response = await fetch('/api/line/welcome', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setWelcomeResult({ success: true, message: data.message });
      } else {
        setWelcomeResult({ success: false, message: data.error || 'Failed to send welcome message' });
      }
    } catch (error) {
      console.error('Error sending welcome message to LINE:', error);
      setWelcomeResult({ success: false, message: 'Network error occurred' });
    } finally {
      setSendingWelcome(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5 text-green-600" />
            <span>LINE Bot Integration</span>
          </CardTitle>
          <CardDescription>
            Configure and test your LINE Bot integration for SMS forwarding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Info */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Setup Instructions</h4>
            <ol className="text-xs text-blue-700 space-y-1 list-decimal list-inside">
              <li>Create a LINE Bot in the LINE Developers Console</li>
              <li>Get your Channel Access Token and Channel Secret</li>
              <li>Add your LINE User ID (you can get this from LINE Bot)</li>
              <li>Configure authorized phone numbers in LINE_AUTHORIZED_NUMBERS</li>
              <li>Set up webhook URL: {typeof window !== 'undefined' ? `${window.location.origin}/api/line/webhook` : '/api/line/webhook'}</li>
              <li>Test the connection using the buttons below</li>
            </ol>
          </div>

          {/* Environment Variables */}
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Required Environment Variables</h4>
            <div className="text-xs text-gray-700 space-y-1 font-mono">
              <div>LINE_CHANNEL_ACCESS_TOKEN=your_channel_access_token</div>
              <div>LINE_CHANNEL_SECRET=your_channel_secret</div>
              <div>LINE_USER_ID=your_user_id</div>
              <div>LINE_AUTHORIZED_NUMBERS=+1234567890,+0987654321</div>
            </div>
          </div>

          {/* Test Connection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Test LINE Bot Connection</h4>
                <p className="text-xs text-muted-foreground">
                  Send a test message to verify your LINE Bot is working
                </p>
              </div>
              <Button
                onClick={testLineConnection}
                disabled={testingConnection}
                variant="outline"
              >
                <Send className="h-4 w-4 mr-2" />
                {testingConnection ? 'Testing...' : 'Test Connection'}
              </Button>
            </div>

            {testResult && (
              <div className={`flex items-center space-x-2 p-3 rounded-md ${
                testResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm ${
                  testResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {testResult.message}
                </span>
              </div>
            )}
          </div>

          {/* Send Statistics */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Send SMS Statistics</h4>
                <p className="text-xs text-muted-foreground">
                  Send current SMS statistics to your LINE account
                </p>
              </div>
              <Button
                onClick={sendStatsToLine}
                disabled={sendingStats}
                variant="outline"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                {sendingStats ? 'Sending...' : 'Send Stats'}
              </Button>
            </div>

            {statsResult && (
              <div className={`flex items-center space-x-2 p-3 rounded-md ${
                statsResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {statsResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm ${
                  statsResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {statsResult.message}
                </span>
              </div>
            )}
          </div>

          {/* Send Welcome Message */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Send Welcome Message</h4>
                <p className="text-xs text-muted-foreground">
                  Send welcome message with quick reply buttons
                </p>
              </div>
              <Button
                onClick={sendWelcomeMessage}
                disabled={sendingWelcome}
                variant="outline"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {sendingWelcome ? 'Sending...' : 'Send Welcome'}
              </Button>
            </div>

            {welcomeResult && (
              <div className={`flex items-center space-x-2 p-3 rounded-md ${
                welcomeResult.success
                  ? 'bg-green-50 border border-green-200'
                  : 'bg-red-50 border border-red-200'
              }`}>
                {welcomeResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className={`text-sm ${
                  welcomeResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {welcomeResult.message}
                </span>
              </div>
            )}
          </div>

          {/* Features */}
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <h4 className="text-sm font-medium text-green-900 mb-2">LINE Bot Features</h4>
            <ul className="text-xs text-green-700 space-y-1 list-disc list-inside">
              <li>🔒 Authorized number filtering - Only configured numbers trigger forwarding</li>
              <li>📱 Automatic SMS forwarding with rich formatting</li>
              <li>⚡ Quick reply buttons for common actions</li>
              <li>🤖 Interactive commands (/devices, /stats, /help, etc.)</li>
              <li>📊 SMS statistics reports on demand</li>
              <li>🔗 Webhook and device status checking</li>
              <li>❓ Built-in help system</li>
              <li>🛡️ Secure webhook signature verification</li>
            </ul>
          </div>

          {/* Commands */}
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-md">
            <h4 className="text-sm font-medium text-purple-900 mb-2">Available Commands</h4>
            <div className="text-xs text-purple-700 space-y-1">
              <div><code>/help</code> - Show help message with quick buttons</div>
              <div><code>/devices</code> - List connected devices and status</div>
              <div><code>/stats</code> - Show SMS statistics</div>
              <div><code>/webhooks</code> - List active webhooks</div>
              <div><code>/inbox</code> - Show inbox information</div>
              <div><code>/send</code> - Get link to send SMS via web interface</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
