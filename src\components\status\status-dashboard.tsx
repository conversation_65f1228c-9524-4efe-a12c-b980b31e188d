'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageState, ProcessState } from '@/types/sms-gateway';
import { getStatusColor, formatDateTime } from '@/lib/utils';
import { RefreshCw, TrendingUp, TrendingDown, Activity } from 'lucide-react';

interface StatusStats {
  total: number;
  pending: number;
  processed: number;
  sent: number;
  delivered: number;
  failed: number;
}

export function StatusDashboard() {
  const [messages, setMessages] = useState<MessageState[]>([]);
  const [stats, setStats] = useState<StatusStats>({
    total: 0,
    pending: 0,
    processed: 0,
    sent: 0,
    delivered: 0,
    failed: 0
  });
  const [loading, setLoading] = useState(false);

  // In a real application, you would fetch this data from your backend
  // For now, we'll use localStorage to simulate persistent storage
  useEffect(() => {
    loadStoredMessages();
  }, []);

  const loadStoredMessages = () => {
    try {
      const stored = localStorage.getItem('sms-messages');
      if (stored) {
        const parsedMessages = JSON.parse(stored);
        setMessages(parsedMessages);
        calculateStats(parsedMessages);
      }
    } catch (error) {
      console.error('Error loading stored messages:', error);
    }
  };

  const calculateStats = (messageList: MessageState[]) => {
    const newStats: StatusStats = {
      total: 0,
      pending: 0,
      processed: 0,
      sent: 0,
      delivered: 0,
      failed: 0
    };

    messageList.forEach(message => {
      message.recipients.forEach(recipient => {
        newStats.total++;
        switch (recipient.state) {
          case ProcessState.Pending:
            newStats.pending++;
            break;
          case ProcessState.Processed:
            newStats.processed++;
            break;
          case ProcessState.Sent:
            newStats.sent++;
            break;
          case ProcessState.Delivered:
            newStats.delivered++;
            break;
          case ProcessState.Failed:
            newStats.failed++;
            break;
        }
      });
    });

    setStats(newStats);
  };

  const refreshAllMessages = async () => {
    if (messages.length === 0) return;
    
    setLoading(true);
    try {
      const updatedMessages = await Promise.all(
        messages.map(async (message) => {
          try {
            const response = await fetch(`/api/messages/${message.id}`);
            if (response.ok) {
              return await response.json();
            }
            return message; // Return original if fetch fails
          } catch (error) {
            console.error(`Error refreshing message ${message.id}:`, error);
            return message;
          }
        })
      );

      setMessages(updatedMessages);
      calculateStats(updatedMessages);
      
      // Update localStorage
      localStorage.setItem('sms-messages', JSON.stringify(updatedMessages));
    } catch (error) {
      console.error('Error refreshing messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSuccessRate = () => {
    if (stats.total === 0) return 0;
    return Math.round((stats.delivered / stats.total) * 100);
  };

  const getFailureRate = () => {
    if (stats.total === 0) return 0;
    return Math.round((stats.failed / stats.total) * 100);
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across {messages.length} message{messages.length !== 1 ? 's' : ''}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{getSuccessRate()}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.delivered} delivered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failure Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{getFailureRate()}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.failed} failed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <RefreshCw className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting processing
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Status Breakdown</CardTitle>
              <CardDescription>Current status of all message recipients</CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshAllMessages}
              disabled={loading || messages.length === 0}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {stats.total === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No messages to display
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {Object.entries(ProcessState).map(([key, status]) => {
                const count = stats[status.toLowerCase() as keyof StatusStats] as number;
                const percentage = stats.total > 0 ? Math.round((count / stats.total) * 100) : 0;
                
                return (
                  <div key={status} className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold mb-1">{count}</div>
                    <div className={`text-xs px-2 py-1 rounded-full mb-2 ${getStatusColor(status)}`}>
                      {status}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {percentage}%
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Messages */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Messages</CardTitle>
          <CardDescription>Latest message activity</CardDescription>
        </CardHeader>
        <CardContent>
          {messages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No messages sent yet
            </div>
          ) : (
            <div className="space-y-3">
              {messages.slice(0, 5).map((message) => {
                const overallStatus = message.recipients.every(r => r.state === ProcessState.Delivered)
                  ? ProcessState.Delivered
                  : message.recipients.some(r => r.state === ProcessState.Failed)
                  ? ProcessState.Failed
                  : message.recipients.some(r => r.state === ProcessState.Sent)
                  ? ProcessState.Sent
                  : ProcessState.Pending;

                return (
                  <div key={message.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">Message {message.id}</div>
                      <div className="text-sm text-muted-foreground">
                        {message.recipients.length} recipient{message.recipients.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(overallStatus)}`}>
                      {overallStatus}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
