'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Device } from '@/types/sms-gateway';
import { Download, Calendar } from 'lucide-react';

export function InboxExportForm() {
  const [devices, setDevices] = useState<Device[]>([]);
  const [selectedDevice, setSelectedDevice] = useState('');
  const [since, setSince] = useState('');
  const [until, setUntil] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    fetchDevices();
    
    // Set default date range (last 7 days)
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    setUntil(now.toISOString().slice(0, 16));
    setSince(weekAgo.toISOString().slice(0, 16));
  }, []);

  const fetchDevices = async () => {
    try {
      const response = await fetch('/api/devices');
      if (response.ok) {
        const data = await response.json();
        setDevices(data);
        if (data.length > 0) {
          setSelectedDevice(data[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching devices:', error);
    }
  };

  const handleExport = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedDevice || !since || !until) {
      setError('Please fill in all fields');
      return;
    }

    if (new Date(since) >= new Date(until)) {
      setError('Start date must be before end date');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await fetch('/api/inbox/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_id: selectedDevice,
          since,
          until,
        }),
      });

      if (response.ok) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 5000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to export inbox');
      }
    } catch (error) {
      console.error('Error exporting inbox:', error);
      setError('Failed to export inbox');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Download className="h-5 w-5" />
          <span>Export Inbox Messages</span>
        </CardTitle>
        <CardDescription>
          Request export of received SMS messages from a specific device and time range
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleExport} className="space-y-4">
          {/* Device Selection */}
          <div>
            <label className="block text-sm font-medium mb-1">Device</label>
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
              required
            >
              <option value="">Select a device</option>
              {devices.map((device) => (
                <option key={device.id} value={device.id}>
                  {device.name} ({device.id})
                </option>
              ))}
            </select>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                <Calendar className="inline h-4 w-4 mr-1" />
                From
              </label>
              <Input
                type="datetime-local"
                value={since}
                onChange={(e) => setSince(e.target.value)}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">
                <Calendar className="inline h-4 w-4 mr-1" />
                To
              </label>
              <Input
                type="datetime-local"
                value={until}
                onChange={(e) => setUntil(e.target.value)}
                required
              />
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-600">
                Export request submitted successfully! The messages will be processed and made available for download.
              </p>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={loading || devices.length === 0}
            className="w-full"
          >
            {loading ? 'Exporting...' : 'Export Messages'}
          </Button>
        </form>

        {devices.length === 0 && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-600">
              No devices available. Please ensure your Android SMS Gateway devices are connected.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
