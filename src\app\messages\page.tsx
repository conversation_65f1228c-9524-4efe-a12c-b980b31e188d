'use client';

import { useState } from 'react';
import { SendMessageForm } from '@/components/messages/send-message-form';
import { MessageStatus } from '@/components/messages/message-status';
import { MessageState } from '@/types/sms-gateway';

export default function MessagesPage() {
  const [sentMessages, setSentMessages] = useState<MessageState[]>([]);

  const handleMessageSent = (messageState: MessageState) => {
    setSentMessages(prev => [messageState, ...prev]);
  };

  const handleRefreshMessage = async (messageId: string) => {
    try {
      const response = await fetch(`/api/messages/${messageId}`);
      if (response.ok) {
        const updatedMessageState = await response.json();
        setSentMessages(prev =>
          prev.map(msg => msg.id === messageId ? updatedMessageState : msg)
        );
      }
    } catch (error) {
      console.error('Error refreshing message status:', error);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Send Messages</h1>
        <p className="text-muted-foreground mt-2">
          Send SMS messages through your Android SMS Gateway
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <SendMessageForm onMessageSent={handleMessageSent} />
        </div>
        
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Recent Messages</h2>
          {sentMessages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No messages sent yet
            </div>
          ) : (
            <div className="space-y-4">
              {sentMessages.map((messageState) => (
                <MessageStatus
                  key={messageState.id}
                  messageState={messageState}
                  onRefresh={() => handleRefreshMessage(messageState.id)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
