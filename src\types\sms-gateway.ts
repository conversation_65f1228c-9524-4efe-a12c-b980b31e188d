// SMS Gateway Types based on android-sms-gateway package

export interface Message {
  id?: string | null;
  message: string;
  ttl?: number | null;
  phoneNumbers: string[];
  simNumber?: number | null;
  withDeliveryReport?: boolean | null;
  priority?: number;
}

export interface TextMessage {
  text: string;
}

export interface DataMessage {
  data: string; // Base64-encoded data
  port: number; // 0-65535
}

export interface MessageRequest {
  id?: string | null;
  textMessage?: TextMessage;
  dataMessage?: DataMessage;
  phoneNumbers: string[];
  simNumber?: number | null;
  ttl?: number | null;
  validUntil?: string | null;
  withDeliveryReport?: boolean | null;
  priority?: number;
  isEncrypted?: boolean;
}

export enum ProcessState {
  Pending = 'Pending',
  Processed = 'Processed',
  Sent = 'Sent',
  Delivered = 'Delivered',
  Failed = 'Failed'
}

export interface RecipientState {
  phoneNumber: string;
  state: ProcessState;
  error?: string;
}

export interface MessageState {
  id: string;
  state: ProcessState;
  recipients: RecipientState[];
}

export enum WebHookEventType {
  SmsReceived = 'sms:received',
  SmsStatusChanged = 'sms:status-changed'
}

export interface WebHook {
  id: string;
  event: WebHookEventType;
  url: string;
  deviceId: string;
}

export interface RegisterWebHookRequest {
  url: string;
  event: WebHookEventType;
  device_id?: string;
}

export interface Device {
  id: string;
  name: string;
  createdAt: string;
  lastSeen: string;
  updatedAt: string;
  deletedAt?: string | null;
}

export interface DeviceSettings {
  messages?: SettingsMessages;
  webhooks?: SettingsWebhooks;
  gateway?: SettingsGateway;
  encryption?: SettingsEncryption;
  logs?: SettingsLogs;
  ping?: SettingsPing;
}

export interface SettingsMessages {
  sendIntervalMin?: number | null;
  sendIntervalMax?: number | null;
  limitPeriod?: 'Disabled' | 'PerMinute' | 'PerHour' | 'PerDay';
  limitValue?: number | null;
  simSelectionMode?: 'OSDefault' | 'RoundRobin' | 'Random';
  logLifetimeDays?: number | null;
}

export interface SettingsWebhooks {
  internetRequired?: boolean;
  retryCount?: number;
  signingKey?: string | null;
}

export interface SettingsGateway {
  cloudUrl?: string;
  privateToken?: string | null;
}

export interface SettingsEncryption {
  passphrase?: string | null;
}

export interface SettingsLogs {
  lifetimeDays?: number;
}

export interface SettingsPing {
  intervalSeconds?: number | null;
}

export enum HealthStatus {
  UP = 'UP',
  DOWN = 'DOWN'
}

export interface HealthCheck {
  status: HealthStatus;
  details?: Record<string, any>;
}

export interface HealthResponse {
  status: HealthStatus;
  version: string;
  releaseId: number;
  checks: { [checkName: string]: HealthCheck };
}

export enum LogEntryPriority {
  VERBOSE = 'VERBOSE',
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

export interface LogEntry {
  id: number;
  createdAt: string;
  module: string;
  priority: LogEntryPriority;
  message: string;
  context?: Record<string, string>;
}

export interface MessagesExportRequest {
  deviceId: string;
  since: string;
  until: string;
}

export interface HttpClient {
  get<T>(url: string, headers?: Record<string, string>): Promise<T>;
  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  delete<T>(url: string, headers?: Record<string, string>): Promise<T>;
}

// Webhook payload types
export interface SmsReceivedWebhookPayload {
  event: WebHookEventType.SmsReceived;
  deviceId: string;
  message: {
    id: string;
    phoneNumber: string;
    text: string;
    receivedAt: string;
  };
}

export interface SmsStatusChangedWebhookPayload {
  event: WebHookEventType.SmsStatusChanged;
  deviceId: string;
  message: {
    id: string;
    state: ProcessState;
    recipients: RecipientState[];
  };
}

export type WebhookPayload = SmsReceivedWebhookPayload | SmsStatusChangedWebhookPayload;
