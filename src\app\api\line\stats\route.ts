import { NextResponse } from 'next/server';
import { getLineBotService, getLineUserId } from '@/lib/line-bot-service';

export async function POST() {
  try {
    const lineBotService = getLineBotService();
    const lineUserId = getLineUserId();
    
    // In a real app, you'd fetch these stats from your database
    // For now, we'll use mock data
    const stats = {
      totalReceived: 42,
      todayReceived: 7,
      lastMessageTime: new Date().toISOString()
    };
    
    await lineBotService.sendSmsStatistics(lineUserId, stats);
    
    return NextResponse.json({ 
      success: true, 
      message: 'SMS statistics sent to LINE successfully' 
    });
  } catch (error) {
    console.error('Error sending SMS statistics to LINE:', error);
    return NextResponse.json(
      { 
        error: 'Failed to send SMS statistics to LINE',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
