import { NextRequest, NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';
import { MessageRequest } from '@/types/sms-gateway';

export async function POST(request: NextRequest) {
  try {
    const messageData: MessageRequest = await request.json();
    const smsService = getSmsGatewayService();
    
    // Get query parameters
    const url = new URL(request.url);
    const skipPhoneValidation = url.searchParams.get('skipPhoneValidation') === 'true';
    
    const messageState = await smsService.sendMessage(messageData, {
      skipPhoneValidation
    });
    
    return NextResponse.json(messageState);
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}
