import { Alert<PERSON><PERSON>gle, Refresh<PERSON><PERSON>, X } from 'lucide-react';
import { Button } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';

interface ErrorDisplayProps {
  error: string | Error;
  title?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  variant?: 'card' | 'inline' | 'banner';
  className?: string;
}

export function ErrorDisplay({
  error,
  title = 'Error',
  onRetry,
  onDismiss,
  variant = 'card',
  className = '',
}: ErrorDisplayProps) {
  const errorMessage = typeof error === 'string' ? error : error.message;

  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 text-red-600 ${className}`}>
        <AlertTriangle className="h-4 w-4" />
        <span className="text-sm">{errorMessage}</span>
        {onRetry && (
          <Button variant="ghost" size="sm" onClick={onRetry}>
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  if (variant === 'banner') {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-md p-4 ${className}`}>
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-red-800">{title}</h3>
            <p className="text-sm text-red-700 mt-1">{errorMessage}</p>
          </div>
          <div className="ml-auto flex space-x-2">
            {onRetry && (
              <Button variant="outline" size="sm" onClick={onRetry}>
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            )}
            {onDismiss && (
              <Button variant="ghost" size="sm" onClick={onDismiss}>
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default card variant
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-red-600">
          <AlertTriangle className="h-5 w-5" />
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <CardDescription className="text-red-600">
          {errorMessage}
        </CardDescription>
        
        {(onRetry || onDismiss) && (
          <div className="flex space-x-2">
            {onRetry && (
              <Button onClick={onRetry} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
            {onDismiss && (
              <Button onClick={onDismiss} variant="ghost">
                Dismiss
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface ApiErrorDisplayProps {
  error: any;
  onRetry?: () => void;
  className?: string;
}

export function ApiErrorDisplay({ error, onRetry, className }: ApiErrorDisplayProps) {
  let errorMessage = 'An unexpected error occurred';
  let statusCode: number | undefined;

  if (error?.response) {
    // Axios-style error
    statusCode = error.response.status;
    errorMessage = error.response.data?.error || error.response.statusText || errorMessage;
  } else if (error?.status) {
    // Fetch-style error
    statusCode = error.status;
    errorMessage = error.message || errorMessage;
  } else if (error?.message) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  }

  const title = statusCode ? `Error ${statusCode}` : 'Error';

  return (
    <ErrorDisplay
      error={errorMessage}
      title={title}
      onRetry={onRetry}
      variant="banner"
      className={className}
    />
  );
}
