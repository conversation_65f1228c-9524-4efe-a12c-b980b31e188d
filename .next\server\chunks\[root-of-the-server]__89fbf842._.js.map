{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/types/sms-gateway.ts"], "sourcesContent": ["// SMS Gateway Types based on android-sms-gateway package\n\nexport interface Message {\n  id?: string | null;\n  message: string;\n  ttl?: number | null;\n  phoneNumbers: string[];\n  simNumber?: number | null;\n  withDeliveryReport?: boolean | null;\n  priority?: number;\n}\n\nexport interface TextMessage {\n  text: string;\n}\n\nexport interface DataMessage {\n  data: string; // Base64-encoded data\n  port: number; // 0-65535\n}\n\nexport interface MessageRequest {\n  id?: string | null;\n  textMessage?: TextMessage;\n  dataMessage?: DataMessage;\n  phoneNumbers: string[];\n  simNumber?: number | null;\n  ttl?: number | null;\n  validUntil?: string | null;\n  withDeliveryReport?: boolean | null;\n  priority?: number;\n  isEncrypted?: boolean;\n}\n\nexport enum ProcessState {\n  Pending = 'Pending',\n  Processed = 'Processed',\n  Sent = 'Sent',\n  Delivered = 'Delivered',\n  Failed = 'Failed'\n}\n\nexport interface RecipientState {\n  phoneNumber: string;\n  state: ProcessState;\n  error?: string;\n}\n\nexport interface MessageState {\n  id: string;\n  state: ProcessState;\n  recipients: RecipientState[];\n}\n\nexport enum WebHookEventType {\n  SmsReceived = 'SmsReceived',\n  SmsStatusChanged = 'SmsStatusChanged'\n}\n\nexport interface WebHook {\n  id: string;\n  event: WebHookEventType;\n  url: string;\n  deviceId: string;\n}\n\nexport interface RegisterWebHookRequest {\n  url: string;\n  event: WebHookEventType;\n  deviceId?: string;\n}\n\nexport interface Device {\n  id: string;\n  name: string;\n  createdAt: string;\n  lastSeen: string;\n  updatedAt: string;\n  deletedAt?: string | null;\n}\n\nexport interface DeviceSettings {\n  messages?: SettingsMessages;\n  webhooks?: SettingsWebhooks;\n  gateway?: SettingsGateway;\n  encryption?: SettingsEncryption;\n  logs?: SettingsLogs;\n  ping?: SettingsPing;\n}\n\nexport interface SettingsMessages {\n  sendIntervalMin?: number | null;\n  sendIntervalMax?: number | null;\n  limitPeriod?: 'Disabled' | 'PerMinute' | 'PerHour' | 'PerDay';\n  limitValue?: number | null;\n  simSelectionMode?: 'OSDefault' | 'RoundRobin' | 'Random';\n  logLifetimeDays?: number | null;\n}\n\nexport interface SettingsWebhooks {\n  internetRequired?: boolean;\n  retryCount?: number;\n  signingKey?: string | null;\n}\n\nexport interface SettingsGateway {\n  cloudUrl?: string;\n  privateToken?: string | null;\n}\n\nexport interface SettingsEncryption {\n  passphrase?: string | null;\n}\n\nexport interface SettingsLogs {\n  lifetimeDays?: number;\n}\n\nexport interface SettingsPing {\n  intervalSeconds?: number | null;\n}\n\nexport enum HealthStatus {\n  UP = 'UP',\n  DOWN = 'DOWN'\n}\n\nexport interface HealthCheck {\n  status: HealthStatus;\n  details?: Record<string, any>;\n}\n\nexport interface HealthResponse {\n  status: HealthStatus;\n  version: string;\n  releaseId: number;\n  checks: { [checkName: string]: HealthCheck };\n}\n\nexport enum LogEntryPriority {\n  VERBOSE = 'VERBOSE',\n  DEBUG = 'DEBUG',\n  INFO = 'INFO',\n  WARN = 'WARN',\n  ERROR = 'ERROR'\n}\n\nexport interface LogEntry {\n  id: number;\n  createdAt: string;\n  module: string;\n  priority: LogEntryPriority;\n  message: string;\n  context?: Record<string, string>;\n}\n\nexport interface MessagesExportRequest {\n  deviceId: string;\n  since: string;\n  until: string;\n}\n\nexport interface HttpClient {\n  get<T>(url: string, headers?: Record<string, string>): Promise<T>;\n  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;\n  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;\n  patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;\n  delete<T>(url: string, headers?: Record<string, string>): Promise<T>;\n}\n\n// Webhook payload types\nexport interface SmsReceivedWebhookPayload {\n  event: WebHookEventType.SmsReceived;\n  deviceId: string;\n  message: {\n    id: string;\n    phoneNumber: string;\n    text: string;\n    receivedAt: string;\n  };\n}\n\nexport interface SmsStatusChangedWebhookPayload {\n  event: WebHookEventType.SmsStatusChanged;\n  deviceId: string;\n  message: {\n    id: string;\n    state: ProcessState;\n    recipients: RecipientState[];\n  };\n}\n\nexport type WebhookPayload = SmsReceivedWebhookPayload | SmsStatusChangedWebhookPayload;\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;AAkClD,IAAA,AAAK,sCAAA;;;;;;WAAA;;AAoBL,IAAA,AAAK,0CAAA;;;WAAA;;AAoEL,IAAA,AAAK,sCAAA;;;WAAA;;AAiBL,IAAA,AAAK,0CAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/app/api/webhooks/receive/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { WebhookPayload, WebHookEventType } from '@/types/sms-gateway';\nimport crypto from 'crypto';\n\n// This endpoint receives webhook notifications from the SMS Gateway\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.text();\n    const payload: WebhookPayload = JSON.parse(body);\n    \n    // Verify webhook signature if secret is configured\n    const webhookSecret = process.env.WEBHOOK_SECRET;\n    if (webhookSecret) {\n      const signature = request.headers.get('x-signature');\n      if (!signature || !verifySignature(body, signature, webhookSecret)) {\n        return NextResponse.json(\n          { error: 'Invalid signature' },\n          { status: 401 }\n        );\n      }\n    }\n\n    // Process the webhook payload\n    await processWebhookPayload(payload);\n    \n    return NextResponse.json({ success: true });\n  } catch (error) {\n    console.error('Error processing webhook:', error);\n    return NextResponse.json(\n      { error: 'Failed to process webhook' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction verifySignature(body: string, signature: string, secret: string): boolean {\n  try {\n    const expectedSignature = crypto\n      .createHmac('sha256', secret)\n      .update(body)\n      .digest('hex');\n    \n    return signature === `sha256=${expectedSignature}`;\n  } catch (error) {\n    console.error('Error verifying signature:', error);\n    return false;\n  }\n}\n\nasync function processWebhookPayload(payload: WebhookPayload) {\n  console.log('Processing webhook payload:', payload);\n  \n  switch (payload.event) {\n    case WebHookEventType.SmsReceived:\n      await handleSmsReceived(payload);\n      break;\n      \n    case WebHookEventType.SmsStatusChanged:\n      await handleSmsStatusChanged(payload);\n      break;\n      \n    default:\n      console.warn('Unknown webhook event type:', payload.event);\n  }\n}\n\nasync function handleSmsReceived(payload: any) {\n  console.log('SMS received:', payload.message);\n  \n  // Store the received message (in a real app, you'd save to database)\n  // For now, we'll use a simple approach to demonstrate\n  try {\n    // You could save to database here\n    // await saveReceivedMessage(payload.message);\n    \n    // For demo purposes, we'll store in a way that can be accessed by the frontend\n    // In production, you'd use a proper database and real-time updates\n    console.log('Received SMS from:', payload.message.phoneNumber);\n    console.log('Message text:', payload.message.text);\n    console.log('Device ID:', payload.deviceId);\n  } catch (error) {\n    console.error('Error handling received SMS:', error);\n  }\n}\n\nasync function handleSmsStatusChanged(payload: any) {\n  console.log('SMS status changed:', payload.message);\n  \n  try {\n    // Update message status in database\n    // await updateMessageStatus(payload.message.id, payload.message.state);\n    \n    console.log('Message status updated:', payload.message.id, payload.message.state);\n  } catch (error) {\n    console.error('Error handling SMS status change:', error);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,UAA0B,KAAK,KAAK,CAAC;QAE3C,mDAAmD;QACnD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc;QAChD,IAAI,eAAe;YACjB,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,gBAAgB,MAAM,WAAW,gBAAgB;gBAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,8BAA8B;QAC9B,MAAM,sBAAsB;QAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,gBAAgB,IAAY,EAAE,SAAiB,EAAE,MAAc;IACtE,IAAI;QACF,MAAM,oBAAoB,qGAAA,CAAA,UAAM,CAC7B,UAAU,CAAC,UAAU,QACrB,MAAM,CAAC,MACP,MAAM,CAAC;QAEV,OAAO,cAAc,CAAC,OAAO,EAAE,mBAAmB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAEA,eAAe,sBAAsB,OAAuB;IAC1D,QAAQ,GAAG,CAAC,+BAA+B;IAE3C,OAAQ,QAAQ,KAAK;QACnB,KAAK,gIAAA,CAAA,mBAAgB,CAAC,WAAW;YAC/B,MAAM,kBAAkB;YACxB;QAEF,KAAK,gIAAA,CAAA,mBAAgB,CAAC,gBAAgB;YACpC,MAAM,uBAAuB;YAC7B;QAEF;YACE,QAAQ,IAAI,CAAC,+BAA+B,QAAQ,KAAK;IAC7D;AACF;AAEA,eAAe,kBAAkB,OAAY;IAC3C,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,OAAO;IAE5C,qEAAqE;IACrE,sDAAsD;IACtD,IAAI;QACF,kCAAkC;QAClC,8CAA8C;QAE9C,+EAA+E;QAC/E,mEAAmE;QACnE,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,WAAW;QAC7D,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,OAAO,CAAC,IAAI;QACjD,QAAQ,GAAG,CAAC,cAAc,QAAQ,QAAQ;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACF;AAEA,eAAe,uBAAuB,OAAY;IAChD,QAAQ,GAAG,CAAC,uBAAuB,QAAQ,OAAO;IAElD,IAAI;QACF,oCAAoC;QACpC,wEAAwE;QAExE,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,KAAK;IAClF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;IACrD;AACF", "debugId": null}}]}