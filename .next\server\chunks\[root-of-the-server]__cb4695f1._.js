module.exports = {

"[project]/.next-internal/server/app/api/settings/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/http-client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "FetchHttpClient": ()=>FetchHttpClient
});
class FetchHttpClient {
    async get(url, headers) {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    async post(url, body, headers) {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...headers
            },
            body: JSON.stringify(body)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    async put(url, body, headers) {
        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                ...headers
            },
            body: JSON.stringify(body)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    async patch(url, body, headers) {
        const response = await fetch(url, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                ...headers
            },
            body: JSON.stringify(body)
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    async delete(url, headers) {
        const response = await fetch(url, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        // Handle empty responses for DELETE operations
        const text = await response.text();
        return text ? JSON.parse(text) : {};
    }
}
}),
"[project]/src/lib/sms-gateway-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SmsGatewayService": ()=>SmsGatewayService,
    "getSmsGatewayService": ()=>getSmsGatewayService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$android$2d$sms$2d$gateway$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/android-sms-gateway/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$http$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/http-client.ts [app-route] (ecmascript)");
;
;
class SmsGatewayService {
    client;
    constructor(login, password, baseUrl = 'https://api.sms-gate.app/3rdparty/v1'){
        const httpClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$http$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FetchHttpClient"]();
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$android$2d$sms$2d$gateway$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"](login, password, httpClient, baseUrl);
    }
    // Message operations
    async sendMessage(message, options) {
        return this.client.send(message, options);
    }
    async getMessageState(messageId) {
        return this.client.getState(messageId);
    }
    // Webhook operations
    async getWebhooks() {
        return this.client.getWebhooks();
    }
    async registerWebhook(request) {
        return this.client.registerWebhook(request);
    }
    async deleteWebhook(webhookId) {
        return this.client.deleteWebhook(webhookId);
    }
    // Device operations
    async getDevices() {
        return this.client.getDevices();
    }
    async deleteDevice(deviceId) {
        return this.client.deleteDevice(deviceId);
    }
    // Health check
    async getHealth() {
        return this.client.getHealth();
    }
    // Inbox export
    async exportInbox(request) {
        return this.client.exportInbox(request);
    }
    // Logs
    async getLogs(from, to) {
        return this.client.getLogs(from, to);
    }
    // Settings operations
    async getSettings() {
        return this.client.getSettings();
    }
    async updateSettings(settings) {
        return this.client.updateSettings(settings);
    }
    async patchSettings(settings) {
        return this.client.patchSettings(settings);
    }
}
// Singleton instance
let smsGatewayService = null;
function getSmsGatewayService() {
    if (!smsGatewayService) {
        const login = process.env.ANDROID_SMS_GATEWAY_LOGIN;
        const password = process.env.ANDROID_SMS_GATEWAY_PASSWORD;
        const baseUrl = process.env.ANDROID_SMS_GATEWAY_BASE_URL;
        if (!login || !password) {
            throw new Error('SMS Gateway credentials not configured');
        }
        smsGatewayService = new SmsGatewayService(login, password, baseUrl);
    }
    return smsGatewayService;
}
}),
"[project]/src/app/api/settings/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "PATCH": ()=>PATCH,
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sms$2d$gateway$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/sms-gateway-service.ts [app-route] (ecmascript)");
;
;
async function GET() {
    try {
        const smsService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sms$2d$gateway$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSmsGatewayService"])();
        const settings = await smsService.getSettings();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(settings);
    } catch (error) {
        console.error('Error fetching settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch settings'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const settings = await request.json();
        const smsService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sms$2d$gateway$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSmsGatewayService"])();
        await smsService.updateSettings(settings);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('Error updating settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to update settings'
        }, {
            status: 500
        });
    }
}
async function PATCH(request) {
    try {
        const partialSettings = await request.json();
        const smsService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sms$2d$gateway$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSmsGatewayService"])();
        await smsService.patchSettings(partialSettings);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('Error patching settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to patch settings'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cb4695f1._.js.map