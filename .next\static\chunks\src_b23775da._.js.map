{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPhoneNumber(phoneNumber: string): string {\n  // Basic E.164 formatting\n  if (!phoneNumber.startsWith('+')) {\n    return `+${phoneNumber}`;\n  }\n  return phoneNumber;\n}\n\nexport function validatePhoneNumber(phoneNumber: string): boolean {\n  // Basic E.164 validation\n  const e164Regex = /^\\+[1-9]\\d{1,14}$/;\n  return e164Regex.test(phoneNumber);\n}\n\nexport function formatDateTime(dateString: string): string {\n  return new Date(dateString).toLocaleString();\n}\n\nexport function formatRelativeTime(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  }\n}\n\nexport function getStatusColor(status: string): string {\n  switch (status.toLowerCase()) {\n    case 'pending':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'processed':\n      return 'text-blue-600 bg-blue-100';\n    case 'sent':\n      return 'text-green-600 bg-green-100';\n    case 'delivered':\n      return 'text-green-700 bg-green-200';\n    case 'failed':\n      return 'text-red-600 bg-red-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\nexport function truncateText(text: string, maxLength: number = 50): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,kBAAkB,WAAmB;IACnD,yBAAyB;IACzB,IAAI,CAAC,YAAY,UAAU,CAAC,MAAM;QAChC,OAAO,AAAC,IAAe,OAAZ;IACb;IACA,OAAO;AACT;AAEO,SAAS,oBAAoB,WAAmB;IACrD,yBAAyB;IACzB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,eAAe,UAAkB;IAC/C,OAAO,IAAI,KAAK,YAAY,cAAc;AAC5C;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,AAAC,GAAmB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM,IAAG;IACpD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;IAC9C,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,AAAC,GAAa,OAAX,MAAK,QAA0B,OAApB,OAAO,IAAI,MAAM,IAAG;IAC3C;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAQ,OAAO,WAAW;QACxB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,aAAa,IAAY;QAAE,YAAA,iEAAoB;IAC7D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { \n  Home, \n  Send, \n  Activity, \n  MessageCircle, \n  Webhook, \n  Settings,\n  Smartphone\n} from 'lucide-react';\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: Home,\n  },\n  {\n    name: 'Send Messages',\n    href: '/messages',\n    icon: Send,\n  },\n  {\n    name: 'Status Tracking',\n    href: '/status',\n    icon: Activity,\n  },\n  {\n    name: 'Inbox',\n    href: '/inbox',\n    icon: MessageCircle,\n  },\n  {\n    name: 'Webhooks',\n    href: '/webhooks',\n    icon: Webhook,\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n  },\n];\n\nexport function Navigation() {\n  const pathname = usePathname();\n\n  return (\n    <nav className=\"bg-white border-r border-gray-200 w-64 min-h-screen\">\n      <div className=\"p-6\">\n        <div className=\"flex items-center space-x-2 mb-8\">\n          <Smartphone className=\"h-8 w-8 text-primary\" />\n          <div>\n            <h1 className=\"text-xl font-bold\">SMS Gateway</h1>\n            <p className=\"text-sm text-muted-foreground\">Android Integration</p>\n          </div>\n        </div>\n        \n        <ul className=\"space-y-2\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href;\n            return (\n              <li key={item.name}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-primary text-primary-foreground'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                  )}\n                >\n                  <item.icon className=\"h-5 w-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              </li>\n            );\n          })}\n        </ul>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAeA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAIjD,6LAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,6LAAC;sCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0FACA,WACI,uCACA;;kDAGN,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;;;;;;2BAXX,KAAK,IAAI;;;;;oBAetB;;;;;;;;;;;;;;;;;AAKV;GAtCgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}]}