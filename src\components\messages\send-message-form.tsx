'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageRequest, MessageState } from '@/types/sms-gateway';
import { formatPhoneNumber, validatePhoneNumber } from '@/lib/utils';
import { Send, Plus, X } from 'lucide-react';

interface SendMessageFormProps {
  onMessageSent: (messageState: MessageState) => void;
}

export function SendMessageForm({ onMessageSent }: SendMessageFormProps) {
  const [phoneNumbers, setPhoneNumbers] = useState<string[]>(['']);
  const [message, setMessage] = useState('');
  const [simNumber, setSimNumber] = useState<number>(1);
  const [priority, setPriority] = useState<number>(0);
  const [ttl, setTtl] = useState<number>(3600);
  const [withDeliveryReport, setWithDeliveryReport] = useState(true);
  const [skipPhoneValidation, setSkipPhoneValidation] = useState(false);
  const [sending, setSending] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const addPhoneNumber = () => {
    setPhoneNumbers([...phoneNumbers, '']);
  };

  const removePhoneNumber = (index: number) => {
    if (phoneNumbers.length > 1) {
      setPhoneNumbers(phoneNumbers.filter((_, i) => i !== index));
    }
  };

  const updatePhoneNumber = (index: number, value: string) => {
    const updated = [...phoneNumbers];
    updated[index] = value;
    setPhoneNumbers(updated);
  };

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!message.trim()) {
      newErrors.push('Message is required');
    }

    const validPhoneNumbers = phoneNumbers.filter(phone => phone.trim());
    if (validPhoneNumbers.length === 0) {
      newErrors.push('At least one phone number is required');
    }

    if (!skipPhoneValidation) {
      validPhoneNumbers.forEach((phone, index) => {
        if (!validatePhoneNumber(formatPhoneNumber(phone))) {
          newErrors.push(`Phone number ${index + 1} is not valid (use E.164 format)`);
        }
      });
    }

    if (priority < -128 || priority > 127) {
      newErrors.push('Priority must be between -128 and 127');
    }

    if (ttl < 1) {
      newErrors.push('TTL must be greater than 0');
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSending(true);
    setErrors([]);

    try {
      const validPhoneNumbers = phoneNumbers
        .filter(phone => phone.trim())
        .map(phone => formatPhoneNumber(phone.trim()));

      const messageRequest: MessageRequest = {
        textMessage: {
          text: message
        },
        phoneNumbers: validPhoneNumbers,
        simNumber,
        priority,
        ttl,
        withDeliveryReport,
      };

      const response = await fetch(`/api/messages?skipPhoneValidation=${skipPhoneValidation}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageRequest),
      });

      if (response.ok) {
        const messageState = await response.json();
        onMessageSent(messageState);
        
        // Reset form
        setPhoneNumbers(['']);
        setMessage('');
        setPriority(0);
        setTtl(3600);
      } else {
        const errorData = await response.json();
        setErrors([errorData.error || 'Failed to send message']);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setErrors(['Failed to send message']);
    } finally {
      setSending(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Send className="h-5 w-5" />
          <span>Send SMS Message</span>
        </CardTitle>
        <CardDescription>
          Send SMS messages through your Android SMS Gateway
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Phone Numbers */}
          <div>
            <label className="block text-sm font-medium mb-2">Phone Numbers</label>
            {phoneNumbers.map((phone, index) => (
              <div key={index} className="flex items-center space-x-2 mb-2">
                <Input
                  type="tel"
                  value={phone}
                  onChange={(e) => updatePhoneNumber(index, e.target.value)}
                  placeholder="+1234567890"
                  className="flex-1"
                />
                {phoneNumbers.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removePhoneNumber(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addPhoneNumber}
              className="mt-2"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Phone Number
            </Button>
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium mb-1">Message</label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your message here..."
              className="w-full min-h-[100px] px-3 py-2 border border-input rounded-md bg-background resize-vertical"
              maxLength={1600}
            />
            <div className="text-sm text-muted-foreground mt-1">
              {message.length}/1600 characters
            </div>
          </div>

          {/* Advanced Options */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">SIM Number</label>
              <select
                value={simNumber}
                onChange={(e) => setSimNumber(parseInt(e.target.value))}
                className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
              >
                <option value={1}>SIM 1</option>
                <option value={2}>SIM 2</option>
                <option value={3}>SIM 3</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Priority</label>
              <Input
                type="number"
                value={priority}
                onChange={(e) => setPriority(parseInt(e.target.value) || 0)}
                min={-128}
                max={127}
                placeholder="0"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">TTL (seconds)</label>
            <Input
              type="number"
              value={ttl}
              onChange={(e) => setTtl(parseInt(e.target.value) || 3600)}
              min={1}
              placeholder="3600"
            />
          </div>

          {/* Checkboxes */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="deliveryReport"
                checked={withDeliveryReport}
                onChange={(e) => setWithDeliveryReport(e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="deliveryReport" className="text-sm font-medium">
                Request delivery report
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="skipValidation"
                checked={skipPhoneValidation}
                onChange={(e) => setSkipPhoneValidation(e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="skipValidation" className="text-sm font-medium">
                Skip phone number validation
              </label>
            </div>
          </div>

          {/* Errors */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <ul className="text-sm text-red-600 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={sending}
            className="w-full"
          >
            {sending ? 'Sending...' : 'Send Message'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
