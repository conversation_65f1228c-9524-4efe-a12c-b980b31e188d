{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAAiE;QAAhE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO;IAC7D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/types/sms-gateway.ts"], "sourcesContent": ["// SMS Gateway Types based on android-sms-gateway package\n\nexport interface Message {\n  id?: string | null;\n  message: string;\n  ttl?: number | null;\n  phoneNumbers: string[];\n  simNumber?: number | null;\n  withDeliveryReport?: boolean | null;\n  priority?: number;\n}\n\nexport interface TextMessage {\n  text: string;\n}\n\nexport interface DataMessage {\n  data: string; // Base64-encoded data\n  port: number; // 0-65535\n}\n\nexport interface MessageRequest {\n  id?: string | null;\n  textMessage?: TextMessage;\n  dataMessage?: DataMessage;\n  phoneNumbers: string[];\n  simNumber?: number | null;\n  ttl?: number | null;\n  validUntil?: string | null;\n  withDeliveryReport?: boolean | null;\n  priority?: number;\n  isEncrypted?: boolean;\n}\n\nexport enum ProcessState {\n  Pending = 'Pending',\n  Processed = 'Processed',\n  Sent = 'Sent',\n  Delivered = 'Delivered',\n  Failed = 'Failed'\n}\n\nexport interface RecipientState {\n  phoneNumber: string;\n  state: ProcessState;\n  error?: string;\n}\n\nexport interface MessageState {\n  id: string;\n  state: ProcessState;\n  recipients: RecipientState[];\n}\n\nexport enum WebHookEventType {\n  SmsReceived = 'SmsReceived',\n  SmsStatusChanged = 'SmsStatusChanged'\n}\n\nexport interface WebHook {\n  id: string;\n  event: WebHookEventType;\n  url: string;\n  deviceId: string;\n}\n\nexport interface RegisterWebHookRequest {\n  url: string;\n  event: WebHookEventType;\n  deviceId?: string;\n}\n\nexport interface Device {\n  id: string;\n  name: string;\n  createdAt: string;\n  lastSeen: string;\n  updatedAt: string;\n  deletedAt?: string | null;\n}\n\nexport interface DeviceSettings {\n  messages?: SettingsMessages;\n  webhooks?: SettingsWebhooks;\n  gateway?: SettingsGateway;\n  encryption?: SettingsEncryption;\n  logs?: SettingsLogs;\n  ping?: SettingsPing;\n}\n\nexport interface SettingsMessages {\n  sendIntervalMin?: number | null;\n  sendIntervalMax?: number | null;\n  limitPeriod?: 'Disabled' | 'PerMinute' | 'PerHour' | 'PerDay';\n  limitValue?: number | null;\n  simSelectionMode?: 'OSDefault' | 'RoundRobin' | 'Random';\n  logLifetimeDays?: number | null;\n}\n\nexport interface SettingsWebhooks {\n  internetRequired?: boolean;\n  retryCount?: number;\n  signingKey?: string | null;\n}\n\nexport interface SettingsGateway {\n  cloudUrl?: string;\n  privateToken?: string | null;\n}\n\nexport interface SettingsEncryption {\n  passphrase?: string | null;\n}\n\nexport interface SettingsLogs {\n  lifetimeDays?: number;\n}\n\nexport interface SettingsPing {\n  intervalSeconds?: number | null;\n}\n\nexport enum HealthStatus {\n  UP = 'UP',\n  DOWN = 'DOWN'\n}\n\nexport interface HealthCheck {\n  status: HealthStatus;\n  details?: Record<string, any>;\n}\n\nexport interface HealthResponse {\n  status: HealthStatus;\n  version: string;\n  releaseId: number;\n  checks: { [checkName: string]: HealthCheck };\n}\n\nexport enum LogEntryPriority {\n  VERBOSE = 'VERBOSE',\n  DEBUG = 'DEBUG',\n  INFO = 'INFO',\n  WARN = 'WARN',\n  ERROR = 'ERROR'\n}\n\nexport interface LogEntry {\n  id: number;\n  createdAt: string;\n  module: string;\n  priority: LogEntryPriority;\n  message: string;\n  context?: Record<string, string>;\n}\n\nexport interface MessagesExportRequest {\n  deviceId: string;\n  since: string;\n  until: string;\n}\n\nexport interface HttpClient {\n  get<T>(url: string, headers?: Record<string, string>): Promise<T>;\n  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;\n  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;\n  patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;\n  delete<T>(url: string, headers?: Record<string, string>): Promise<T>;\n}\n\n// Webhook payload types\nexport interface SmsReceivedWebhookPayload {\n  event: WebHookEventType.SmsReceived;\n  deviceId: string;\n  message: {\n    id: string;\n    phoneNumber: string;\n    text: string;\n    receivedAt: string;\n  };\n}\n\nexport interface SmsStatusChangedWebhookPayload {\n  event: WebHookEventType.SmsStatusChanged;\n  deviceId: string;\n  message: {\n    id: string;\n    state: ProcessState;\n    recipients: RecipientState[];\n  };\n}\n\nexport type WebhookPayload = SmsReceivedWebhookPayload | SmsStatusChangedWebhookPayload;\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;AAkClD,IAAA,AAAK,sCAAA;;;;;;WAAA;;AAoBL,IAAA,AAAK,0CAAA;;;WAAA;;AAoEL,IAAA,AAAK,sCAAA;;;WAAA;;AAiBL,IAAA,AAAK,0CAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/messages/message-status.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { MessageState, ProcessState } from '@/types/sms-gateway';\nimport { getStatusColor, formatDateTime } from '@/lib/utils';\nimport { RefreshCw, CheckCircle, XCircle, Clock, Send } from 'lucide-react';\n\ninterface MessageStatusProps {\n  messageState: MessageState;\n  onRefresh?: () => void;\n}\n\nexport function MessageStatus({ messageState, onRefresh }: MessageStatusProps) {\n  const [refreshing, setRefreshing] = useState(false);\n\n  const handleRefresh = async () => {\n    if (!onRefresh) return;\n    \n    setRefreshing(true);\n    try {\n      await onRefresh();\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  const getStatusIcon = (status: ProcessState) => {\n    switch (status) {\n      case ProcessState.Pending:\n        return <Clock className=\"h-4 w-4\" />;\n      case ProcessState.Processed:\n        return <Send className=\"h-4 w-4\" />;\n      case ProcessState.Sent:\n        return <CheckCircle className=\"h-4 w-4\" />;\n      case ProcessState.Delivered:\n        return <CheckCircle className=\"h-4 w-4\" />;\n      case ProcessState.Failed:\n        return <XCircle className=\"h-4 w-4\" />;\n      default:\n        return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getOverallStatus = () => {\n    const statuses = messageState.recipients.map(r => r.state);\n    \n    if (statuses.every(s => s === ProcessState.Delivered)) {\n      return { status: ProcessState.Delivered, color: 'text-green-700 bg-green-200' };\n    }\n    \n    if (statuses.some(s => s === ProcessState.Failed)) {\n      return { status: ProcessState.Failed, color: 'text-red-600 bg-red-100' };\n    }\n    \n    if (statuses.some(s => s === ProcessState.Sent)) {\n      return { status: ProcessState.Sent, color: 'text-green-600 bg-green-100' };\n    }\n    \n    if (statuses.some(s => s === ProcessState.Processed)) {\n      return { status: ProcessState.Processed, color: 'text-blue-600 bg-blue-100' };\n    }\n    \n    return { status: ProcessState.Pending, color: 'text-yellow-600 bg-yellow-100' };\n  };\n\n  const overallStatus = getOverallStatus();\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center space-x-2\">\n              {getStatusIcon(overallStatus.status)}\n              <span>Message Status</span>\n            </CardTitle>\n            <CardDescription>\n              Message ID: {messageState.id}\n            </CardDescription>\n          </div>\n          \n          {onRefresh && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleRefresh}\n              disabled={refreshing}\n            >\n              <RefreshCw className={`h-4 w-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />\n              Refresh\n            </Button>\n          )}\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-4\">\n        {/* Overall Status */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm font-medium\">Overall Status:</span>\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${overallStatus.color}`}>\n            {overallStatus.status}\n          </span>\n        </div>\n\n        {/* Recipients Status */}\n        <div>\n          <h4 className=\"text-sm font-medium mb-3\">Recipients ({messageState.recipients.length})</h4>\n          <div className=\"space-y-2\">\n            {messageState.recipients.map((recipient, index) => (\n              <div\n                key={index}\n                className=\"flex items-center justify-between p-3 border rounded-lg\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  {getStatusIcon(recipient.state)}\n                  <div>\n                    <div className=\"font-medium\">{recipient.phoneNumber}</div>\n                    {recipient.error && (\n                      <div className=\"text-sm text-red-600 mt-1\">\n                        Error: {recipient.error}\n                      </div>\n                    )}\n                  </div>\n                </div>\n                \n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(recipient.state)}`}>\n                  {recipient.state}\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Status Summary */}\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 pt-4 border-t\">\n          {Object.values(ProcessState).map(status => {\n            const count = messageState.recipients.filter(r => r.state === status).length;\n            return (\n              <div key={status} className=\"text-center\">\n                <div className=\"text-lg font-semibold\">{count}</div>\n                <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(status)}`}>\n                  {status}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAcO,SAAS,cAAc,KAA+C;QAA/C,EAAE,YAAY,EAAE,SAAS,EAAsB,GAA/C;;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW;QAEhB,cAAc;QACd,IAAI;YACF,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK,iIAAA,CAAA,eAAY,CAAC,OAAO;gBACvB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,iIAAA,CAAA,eAAY,CAAC,SAAS;gBACzB,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK,iIAAA,CAAA,eAAY,CAAC,IAAI;gBACpB,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK,iIAAA,CAAA,eAAY,CAAC,SAAS;gBACzB,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK,iIAAA,CAAA,eAAY,CAAC,MAAM;gBACtB,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,aAAa,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QAEzD,IAAI,SAAS,KAAK,CAAC,CAAA,IAAK,MAAM,iIAAA,CAAA,eAAY,CAAC,SAAS,GAAG;YACrD,OAAO;gBAAE,QAAQ,iIAAA,CAAA,eAAY,CAAC,SAAS;gBAAE,OAAO;YAA8B;QAChF;QAEA,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,MAAM,iIAAA,CAAA,eAAY,CAAC,MAAM,GAAG;YACjD,OAAO;gBAAE,QAAQ,iIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE,OAAO;YAA0B;QACzE;QAEA,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,MAAM,iIAAA,CAAA,eAAY,CAAC,IAAI,GAAG;YAC/C,OAAO;gBAAE,QAAQ,iIAAA,CAAA,eAAY,CAAC,IAAI;gBAAE,OAAO;YAA8B;QAC3E;QAEA,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,MAAM,iIAAA,CAAA,eAAY,CAAC,SAAS,GAAG;YACpD,OAAO;gBAAE,QAAQ,iIAAA,CAAA,eAAY,CAAC,SAAS;gBAAE,OAAO;YAA4B;QAC9E;QAEA,OAAO;YAAE,QAAQ,iIAAA,CAAA,eAAY,CAAC,OAAO;YAAE,OAAO;QAAgC;IAChF;IAEA,MAAM,gBAAgB;IAEtB,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;wCAClB,cAAc,cAAc,MAAM;sDACnC,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,mIAAA,CAAA,kBAAe;;wCAAC;wCACF,aAAa,EAAE;;;;;;;;;;;;;wBAI/B,2BACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAW,AAAC,gBAAgD,OAAjC,aAAa,iBAAiB;;;;;;gCAAQ;;;;;;;;;;;;;;;;;;0BAOpF,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,6LAAC;gCAAK,WAAW,AAAC,8CAAiE,OAApB,cAAc,KAAK;0CAC/E,cAAc,MAAM;;;;;;;;;;;;kCAKzB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAA2B;oCAAa,aAAa,UAAU,CAAC,MAAM;oCAAC;;;;;;;0CACrF,6LAAC;gCAAI,WAAU;0CACZ,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACvC,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,UAAU,KAAK;kEAC9B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,UAAU,WAAW;;;;;;4DAClD,UAAU,KAAK,kBACd,6LAAC;gEAAI,WAAU;;oEAA4B;oEACjC,UAAU,KAAK;;;;;;;;;;;;;;;;;;;0DAM/B,6LAAC;gDAAK,WAAW,AAAC,8CAA6E,OAAhC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,KAAK;0DAC1F,UAAU,KAAK;;;;;;;uCAhBb;;;;;;;;;;;;;;;;kCAwBb,6LAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,CAAC,iIAAA,CAAA,eAAY,EAAE,GAAG,CAAC,CAAA;4BAC/B,MAAM,QAAQ,aAAa,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,MAAM;4BAC5E,qBACE,6LAAC;gCAAiB,WAAU;;kDAC1B,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;kDACxC,6LAAC;wCAAI,WAAW,AAAC,kCAAwD,OAAvB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;kDAC9D;;;;;;;+BAHK;;;;;wBAOd;;;;;;;;;;;;;;;;;;AAKV;GA1IgB;KAAA", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/status/message-tracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { MessageStatus } from '@/components/messages/message-status';\nimport { MessageState } from '@/types/sms-gateway';\nimport { Search } from 'lucide-react';\n\nexport function MessageTracker() {\n  const [messageId, setMessageId] = useState('');\n  const [messageState, setMessageState] = useState<MessageState | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!messageId.trim()) {\n      setError('Please enter a message ID');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setMessageState(null);\n\n    try {\n      const response = await fetch(`/api/messages/${messageId.trim()}`);\n      \n      if (response.ok) {\n        const data = await response.json();\n        setMessageState(data);\n      } else if (response.status === 404) {\n        setError('Message not found');\n      } else {\n        setError('Failed to fetch message status');\n      }\n    } catch (error) {\n      console.error('Error fetching message status:', error);\n      setError('Failed to fetch message status');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    if (!messageState) return;\n    \n    setLoading(true);\n    try {\n      const response = await fetch(`/api/messages/${messageState.id}`);\n      if (response.ok) {\n        const data = await response.json();\n        setMessageState(data);\n      }\n    } catch (error) {\n      console.error('Error refreshing message status:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Search className=\"h-5 w-5\" />\n            <span>Track Message</span>\n          </CardTitle>\n          <CardDescription>\n            Enter a message ID to track its delivery status\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSearch} className=\"flex space-x-2\">\n            <Input\n              type=\"text\"\n              value={messageId}\n              onChange={(e) => setMessageId(e.target.value)}\n              placeholder=\"Enter message ID...\"\n              className=\"flex-1\"\n            />\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? 'Searching...' : 'Track'}\n            </Button>\n          </form>\n          \n          {error && (\n            <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-sm text-red-600\">{error}</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {messageState && (\n        <MessageStatus\n          messageState={messageState}\n          onRefresh={handleRefresh}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;;;AARA;;;;;;;AAUO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAiC,OAAjB,UAAU,IAAI;YAE5D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB;YAClB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;QAEnB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAgC,OAAhB,aAAa,EAAE;YAC7D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;kDAC7B,UAAU,iBAAiB;;;;;;;;;;;;4BAI/B,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;YAM5C,8BACC,6LAAC,sJAAA,CAAA,gBAAa;gBACZ,cAAc;gBACd,WAAW;;;;;;;;;;;;AAKrB;GAhGgB;KAAA", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/status/status-dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { MessageState, ProcessState } from '@/types/sms-gateway';\nimport { getStatusColor, formatDateTime } from '@/lib/utils';\nimport { RefreshCw, TrendingUp, TrendingDown, Activity } from 'lucide-react';\n\ninterface StatusStats {\n  total: number;\n  pending: number;\n  processed: number;\n  sent: number;\n  delivered: number;\n  failed: number;\n}\n\nexport function StatusDashboard() {\n  const [messages, setMessages] = useState<MessageState[]>([]);\n  const [stats, setStats] = useState<StatusStats>({\n    total: 0,\n    pending: 0,\n    processed: 0,\n    sent: 0,\n    delivered: 0,\n    failed: 0\n  });\n  const [loading, setLoading] = useState(false);\n\n  // In a real application, you would fetch this data from your backend\n  // For now, we'll use localStorage to simulate persistent storage\n  useEffect(() => {\n    loadStoredMessages();\n  }, []);\n\n  const loadStoredMessages = () => {\n    try {\n      const stored = localStorage.getItem('sms-messages');\n      if (stored) {\n        const parsedMessages = JSON.parse(stored);\n        setMessages(parsedMessages);\n        calculateStats(parsedMessages);\n      }\n    } catch (error) {\n      console.error('Error loading stored messages:', error);\n    }\n  };\n\n  const calculateStats = (messageList: MessageState[]) => {\n    const newStats: StatusStats = {\n      total: 0,\n      pending: 0,\n      processed: 0,\n      sent: 0,\n      delivered: 0,\n      failed: 0\n    };\n\n    messageList.forEach(message => {\n      message.recipients.forEach(recipient => {\n        newStats.total++;\n        switch (recipient.state) {\n          case ProcessState.Pending:\n            newStats.pending++;\n            break;\n          case ProcessState.Processed:\n            newStats.processed++;\n            break;\n          case ProcessState.Sent:\n            newStats.sent++;\n            break;\n          case ProcessState.Delivered:\n            newStats.delivered++;\n            break;\n          case ProcessState.Failed:\n            newStats.failed++;\n            break;\n        }\n      });\n    });\n\n    setStats(newStats);\n  };\n\n  const refreshAllMessages = async () => {\n    if (messages.length === 0) return;\n    \n    setLoading(true);\n    try {\n      const updatedMessages = await Promise.all(\n        messages.map(async (message) => {\n          try {\n            const response = await fetch(`/api/messages/${message.id}`);\n            if (response.ok) {\n              return await response.json();\n            }\n            return message; // Return original if fetch fails\n          } catch (error) {\n            console.error(`Error refreshing message ${message.id}:`, error);\n            return message;\n          }\n        })\n      );\n\n      setMessages(updatedMessages);\n      calculateStats(updatedMessages);\n      \n      // Update localStorage\n      localStorage.setItem('sms-messages', JSON.stringify(updatedMessages));\n    } catch (error) {\n      console.error('Error refreshing messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getSuccessRate = () => {\n    if (stats.total === 0) return 0;\n    return Math.round((stats.delivered / stats.total) * 100);\n  };\n\n  const getFailureRate = () => {\n    if (stats.total === 0) return 0;\n    return Math.round((stats.failed / stats.total) * 100);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Messages</CardTitle>\n            <Activity className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.total}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Across {messages.length} message{messages.length !== 1 ? 's' : ''}\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Success Rate</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-green-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-green-600\">{getSuccessRate()}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              {stats.delivered} delivered\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Failure Rate</CardTitle>\n            <TrendingDown className=\"h-4 w-4 text-red-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">{getFailureRate()}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              {stats.failed} failed\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\n            <RefreshCw className=\"h-4 w-4 text-yellow-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-yellow-600\">{stats.pending}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Awaiting processing\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Status Breakdown */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle>Status Breakdown</CardTitle>\n              <CardDescription>Current status of all message recipients</CardDescription>\n            </div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={refreshAllMessages}\n              disabled={loading || messages.length === 0}\n            >\n              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />\n              Refresh All\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {stats.total === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No messages to display\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n              {Object.entries(ProcessState).map(([key, status]) => {\n                const count = stats[status.toLowerCase() as keyof StatusStats] as number;\n                const percentage = stats.total > 0 ? Math.round((count / stats.total) * 100) : 0;\n                \n                return (\n                  <div key={status} className=\"text-center p-4 border rounded-lg\">\n                    <div className=\"text-2xl font-bold mb-1\">{count}</div>\n                    <div className={`text-xs px-2 py-1 rounded-full mb-2 ${getStatusColor(status)}`}>\n                      {status}\n                    </div>\n                    <div className=\"text-xs text-muted-foreground\">\n                      {percentage}%\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Recent Messages */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Messages</CardTitle>\n          <CardDescription>Latest message activity</CardDescription>\n        </CardHeader>\n        <CardContent>\n          {messages.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No messages sent yet\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {messages.slice(0, 5).map((message) => {\n                const overallStatus = message.recipients.every(r => r.state === ProcessState.Delivered)\n                  ? ProcessState.Delivered\n                  : message.recipients.some(r => r.state === ProcessState.Failed)\n                  ? ProcessState.Failed\n                  : message.recipients.some(r => r.state === ProcessState.Sent)\n                  ? ProcessState.Sent\n                  : ProcessState.Pending;\n\n                return (\n                  <div key={message.id} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                    <div>\n                      <div className=\"font-medium\">Message {message.id}</div>\n                      <div className=\"text-sm text-muted-foreground\">\n                        {message.recipients.length} recipient{message.recipients.length !== 1 ? 's' : ''}\n                      </div>\n                    </div>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(overallStatus)}`}>\n                      {overallStatus}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAkBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC9C,OAAO;QACP,SAAS;QACT,WAAW;QACX,MAAM;QACN,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,qEAAqE;IACrE,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAClC,YAAY;gBACZ,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAwB;YAC5B,OAAO;YACP,SAAS;YACT,WAAW;YACX,MAAM;YACN,WAAW;YACX,QAAQ;QACV;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,QAAQ,UAAU,CAAC,OAAO,CAAC,CAAA;gBACzB,SAAS,KAAK;gBACd,OAAQ,UAAU,KAAK;oBACrB,KAAK,iIAAA,CAAA,eAAY,CAAC,OAAO;wBACvB,SAAS,OAAO;wBAChB;oBACF,KAAK,iIAAA,CAAA,eAAY,CAAC,SAAS;wBACzB,SAAS,SAAS;wBAClB;oBACF,KAAK,iIAAA,CAAA,eAAY,CAAC,IAAI;wBACpB,SAAS,IAAI;wBACb;oBACF,KAAK,iIAAA,CAAA,eAAY,CAAC,SAAS;wBACzB,SAAS,SAAS;wBAClB;oBACF,KAAK,iIAAA,CAAA,eAAY,CAAC,MAAM;wBACtB,SAAS,MAAM;wBACf;gBACJ;YACF;QACF;QAEA,SAAS;IACX;IAEA,MAAM,qBAAqB;QACzB,IAAI,SAAS,MAAM,KAAK,GAAG;QAE3B,WAAW;QACX,IAAI;YACF,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CACvC,SAAS,GAAG,CAAC,OAAO;gBAClB,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA2B,OAAX,QAAQ,EAAE;oBACxD,IAAI,SAAS,EAAE,EAAE;wBACf,OAAO,MAAM,SAAS,IAAI;oBAC5B;oBACA,OAAO,SAAS,iCAAiC;gBACnD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,4BAAsC,OAAX,QAAQ,EAAE,EAAC,MAAI;oBACzD,OAAO;gBACT;YACF;YAGF,YAAY;YACZ,eAAe;YAEf,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM,KAAK,KAAK,GAAG,OAAO;QAC9B,OAAO,KAAK,KAAK,CAAC,AAAC,MAAM,SAAS,GAAG,MAAM,KAAK,GAAI;IACtD;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM,KAAK,KAAK,GAAG,OAAO;QAC9B,OAAO,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;IACnD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDAAsB,MAAM,KAAK;;;;;;kDAChD,6LAAC;wCAAE,WAAU;;4CAAgC;4CACnC,SAAS,MAAM;4CAAC;4CAAS,SAAS,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;kCAKrE,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;;4CAAqC;4CAAiB;;;;;;;kDACrE,6LAAC;wCAAE,WAAU;;4CACV,MAAM,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;0CAE1B,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;;4CAAmC;4CAAiB;;;;;;;kDACnE,6LAAC;wCAAE,WAAU;;4CACV,MAAM,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAKpB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDAAsC,MAAM,OAAO;;;;;;kDAClE,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,WAAW,SAAS,MAAM,KAAK;;sDAEzC,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAW,AAAC,gBAA6C,OAA9B,UAAU,iBAAiB;;;;;;wCAAQ;;;;;;;;;;;;;;;;;;kCAK/E,6LAAC,mIAAA,CAAA,cAAW;kCACT,MAAM,KAAK,KAAK,kBACf,6LAAC;4BAAI,WAAU;sCAAyC;;;;;iDAIxD,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,iIAAA,CAAA,eAAY,EAAE,GAAG,CAAC;oCAAC,CAAC,KAAK,OAAO;gCAC9C,MAAM,QAAQ,KAAK,CAAC,OAAO,WAAW,GAAwB;gCAC9D,MAAM,aAAa,MAAM,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,QAAQ,MAAM,KAAK,GAAI,OAAO;gCAE/E,qBACE,6LAAC;oCAAiB,WAAU;;sDAC1B,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;sDAC1C,6LAAC;4CAAI,WAAW,AAAC,uCAA6D,OAAvB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;sDACnE;;;;;;sDAEH,6LAAC;4CAAI,WAAU;;gDACZ;gDAAW;;;;;;;;mCANN;;;;;4BAUd;;;;;;;;;;;;;;;;;0BAOR,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACT,SAAS,MAAM,KAAK,kBACnB,6LAAC;4BAAI,WAAU;sCAAyC;;;;;iDAIxD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gCACzB,MAAM,gBAAgB,QAAQ,UAAU,CAAC,KAAK,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,iIAAA,CAAA,eAAY,CAAC,SAAS,IAClF,iIAAA,CAAA,eAAY,CAAC,SAAS,GACtB,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,iIAAA,CAAA,eAAY,CAAC,MAAM,IAC5D,iIAAA,CAAA,eAAY,CAAC,MAAM,GACnB,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,iIAAA,CAAA,eAAY,CAAC,IAAI,IAC1D,iIAAA,CAAA,eAAY,CAAC,IAAI,GACjB,iIAAA,CAAA,eAAY,CAAC,OAAO;gCAExB,qBACE,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;wDAAc;wDAAS,QAAQ,EAAE;;;;;;;8DAChD,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,UAAU,CAAC,MAAM;wDAAC;wDAAW,QAAQ,UAAU,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;sDAGlF,6LAAC;4CAAK,WAAW,AAAC,8CAA2E,OAA9B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;sDAC3E;;;;;;;mCARK,QAAQ,EAAE;;;;;4BAYxB;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA/PgB;KAAA", "debugId": null}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/app/status/page.tsx"], "sourcesContent": ["'use client';\n\nimport { MessageTracker } from '@/components/status/message-tracker';\nimport { StatusDashboard } from '@/components/status/status-dashboard';\n\nexport default function StatusPage() {\n  return (\n    <div className=\"container mx-auto py-8 px-4\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold\">Status Tracking</h1>\n        <p className=\"text-muted-foreground mt-2\">\n          Monitor message delivery status and track individual messages\n        </p>\n      </div>\n\n      <div className=\"space-y-8\">\n        <StatusDashboard />\n        <MessageTracker />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,sJAAA,CAAA,kBAAe;;;;;kCAChB,6LAAC,qJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;AAIvB;KAhBwB", "debugId": null}}]}