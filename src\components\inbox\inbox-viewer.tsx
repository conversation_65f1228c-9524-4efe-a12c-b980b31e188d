'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { formatDateTime, formatRelativeTime, truncateText } from '@/lib/utils';
import { MessageCircle, Search, RefreshCw, Filter } from 'lucide-react';

interface InboxMessage {
  id: string;
  phoneNumber: string;
  text: string;
  receivedAt: string;
  deviceId: string;
  deviceName: string;
}

export function InboxViewer() {
  const [messages, setMessages] = useState<InboxMessage[]>([]);
  const [filteredMessages, setFilteredMessages] = useState<InboxMessage[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPhone, setFilterPhone] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Load messages from localStorage (simulating received messages)
    loadStoredMessages();
  }, []);

  useEffect(() => {
    // Filter messages based on search term and phone filter
    let filtered = messages;

    if (searchTerm) {
      filtered = filtered.filter(msg =>
        msg.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msg.phoneNumber.includes(searchTerm)
      );
    }

    if (filterPhone) {
      filtered = filtered.filter(msg =>
        msg.phoneNumber.includes(filterPhone)
      );
    }

    setFilteredMessages(filtered);
  }, [messages, searchTerm, filterPhone]);

  const loadStoredMessages = () => {
    try {
      const stored = localStorage.getItem('inbox-messages');
      if (stored) {
        const parsedMessages = JSON.parse(stored);
        setMessages(parsedMessages);
      } else {
        // Add some sample messages for demonstration
        const sampleMessages: InboxMessage[] = [
          {
            id: 'msg-1',
            phoneNumber: '+1234567890',
            text: 'Hello! This is a test message.',
            receivedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            deviceId: 'device-1',
            deviceName: 'My Phone'
          },
          {
            id: 'msg-2',
            phoneNumber: '+0987654321',
            text: 'Your verification code is 123456',
            receivedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
            deviceId: 'device-1',
            deviceName: 'My Phone'
          },
          {
            id: 'msg-3',
            phoneNumber: '+1234567890',
            text: 'Thanks for your message! We will get back to you soon.',
            receivedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            deviceId: 'device-1',
            deviceName: 'My Phone'
          }
        ];
        setMessages(sampleMessages);
        localStorage.setItem('inbox-messages', JSON.stringify(sampleMessages));
      }
    } catch (error) {
      console.error('Error loading stored messages:', error);
    }
  };

  const handleRefresh = async () => {
    setLoading(true);
    // Simulate API call delay
    setTimeout(() => {
      loadStoredMessages();
      setLoading(false);
    }, 1000);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setFilterPhone('');
  };

  const getUniquePhoneNumbers = () => {
    const phones = messages.map(msg => msg.phoneNumber);
    return [...new Set(phones)].sort();
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filter Messages</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Search Text</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search messages..."
                  className="pl-10"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Filter by Phone</label>
              <select
                value={filterPhone}
                onChange={(e) => setFilterPhone(e.target.value)}
                className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
              >
                <option value="">All phone numbers</option>
                {getUniquePhoneNumbers().map(phone => (
                  <option key={phone} value={phone}>{phone}</option>
                ))}
              </select>
            </div>
            
            <div className="flex items-end space-x-2">
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
              <Button variant="outline" onClick={handleRefresh} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MessageCircle className="h-5 w-5" />
              <span>Received Messages</span>
            </div>
            <span className="text-sm font-normal text-muted-foreground">
              {filteredMessages.length} of {messages.length} messages
            </span>
          </CardTitle>
          <CardDescription>
            Messages received through your SMS Gateway devices
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredMessages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {messages.length === 0 ? 'No messages received yet' : 'No messages match your filters'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-medium">{message.phoneNumber}</span>
                        <span className="text-xs text-muted-foreground px-2 py-1 bg-muted rounded">
                          {message.deviceName}
                        </span>
                      </div>
                      
                      <p className="text-sm mb-2">{message.text}</p>
                      
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <span>{formatRelativeTime(message.receivedAt)}</span>
                        <span>{formatDateTime(message.receivedAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
