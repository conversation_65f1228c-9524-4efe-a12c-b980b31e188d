{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAAiE;QAAhE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO;IAC7D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/settings/device-settings-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { DeviceSettings } from '@/types/sms-gateway';\n\ninterface DeviceSettingsFormProps {\n  onSave: (settings: DeviceSettings) => Promise<void>;\n  loading?: boolean;\n}\n\nexport function DeviceSettingsForm({ onSave, loading = false }: DeviceSettingsFormProps) {\n  const [settings, setSettings] = useState<DeviceSettings>({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n\n  const fetchSettings = async () => {\n    try {\n      const response = await fetch('/api/settings');\n      if (response.ok) {\n        const data = await response.json();\n        setSettings(data);\n      }\n    } catch (error) {\n      console.error('Error fetching settings:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    try {\n      await onSave(settings);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const updateSettings = (section: keyof DeviceSettings, field: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  if (isLoading) {\n    return <div className=\"flex justify-center p-8\">Loading settings...</div>;\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Messages Settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Message Settings</CardTitle>\n          <CardDescription>Configure message sending behavior</CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-1\">Send Interval Min (ms)</label>\n              <Input\n                type=\"number\"\n                value={settings.messages?.sendIntervalMin || ''}\n                onChange={(e) => updateSettings('messages', 'sendIntervalMin', \n                  e.target.value ? parseInt(e.target.value) : null)}\n                placeholder=\"Minimum interval\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-1\">Send Interval Max (ms)</label>\n              <Input\n                type=\"number\"\n                value={settings.messages?.sendIntervalMax || ''}\n                onChange={(e) => updateSettings('messages', 'sendIntervalMax', \n                  e.target.value ? parseInt(e.target.value) : null)}\n                placeholder=\"Maximum interval\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-1\">Limit Period</label>\n              <select\n                className=\"w-full h-10 px-3 py-2 border border-input rounded-md bg-background\"\n                value={settings.messages?.limitPeriod || 'Disabled'}\n                onChange={(e) => updateSettings('messages', 'limitPeriod', e.target.value)}\n              >\n                <option value=\"Disabled\">Disabled</option>\n                <option value=\"PerMinute\">Per Minute</option>\n                <option value=\"PerHour\">Per Hour</option>\n                <option value=\"PerDay\">Per Day</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-1\">Limit Value</label>\n              <Input\n                type=\"number\"\n                value={settings.messages?.limitValue || ''}\n                onChange={(e) => updateSettings('messages', 'limitValue', \n                  e.target.value ? parseInt(e.target.value) : null)}\n                placeholder=\"Message limit\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium mb-1\">SIM Selection Mode</label>\n            <select\n              className=\"w-full h-10 px-3 py-2 border border-input rounded-md bg-background\"\n              value={settings.messages?.simSelectionMode || 'OSDefault'}\n              onChange={(e) => updateSettings('messages', 'simSelectionMode', e.target.value)}\n            >\n              <option value=\"OSDefault\">OS Default</option>\n              <option value=\"RoundRobin\">Round Robin</option>\n              <option value=\"Random\">Random</option>\n            </select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Webhook Settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Webhook Settings</CardTitle>\n          <CardDescription>Configure webhook behavior</CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              id=\"internetRequired\"\n              checked={settings.webhooks?.internetRequired || false}\n              onChange={(e) => updateSettings('webhooks', 'internetRequired', e.target.checked)}\n              className=\"rounded border-input\"\n            />\n            <label htmlFor=\"internetRequired\" className=\"text-sm font-medium\">\n              Internet Required\n            </label>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium mb-1\">Retry Count</label>\n            <Input\n              type=\"number\"\n              value={settings.webhooks?.retryCount || ''}\n              onChange={(e) => updateSettings('webhooks', 'retryCount', \n                e.target.value ? parseInt(e.target.value) : 1)}\n              placeholder=\"Number of retries\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Logs Settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Log Settings</CardTitle>\n          <CardDescription>Configure log retention</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div>\n            <label className=\"block text-sm font-medium mb-1\">Log Lifetime (days)</label>\n            <Input\n              type=\"number\"\n              value={settings.logs?.lifetimeDays || ''}\n              onChange={(e) => updateSettings('logs', 'lifetimeDays', \n                e.target.value ? parseInt(e.target.value) : 30)}\n              placeholder=\"Days to keep logs\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      <div className=\"flex justify-end\">\n        <Button \n          onClick={handleSave} \n          disabled={isSaving || loading}\n          className=\"min-w-[120px]\"\n        >\n          {isSaving ? 'Saving...' : 'Save Settings'}\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaO,SAAS,mBAAmB,KAAoD;QAApD,EAAE,MAAM,EAAE,UAAU,KAAK,EAA2B,GAApD;QA4DZ,oBAUA,qBAaA,qBAaA,qBAYF,qBAsBE,oBAaF,qBAoBA;;IAlKnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;QACf,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB,CAAC,SAA+B,OAAe;QACpE,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;oBACT,GAAG,IAAI,CAAC,QAAQ;oBAChB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC;YAAI,WAAU;sBAA0B;;;;;;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,EAAA,qBAAA,SAAS,QAAQ,cAAjB,yCAAA,mBAAmB,eAAe,KAAI;gDAC7C,UAAU,CAAC,IAAM,eAAe,YAAY,mBAC1C,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC9C,aAAY;;;;;;;;;;;;kDAGhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,EAAA,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,eAAe,KAAI;gDAC7C,UAAU,CAAC,IAAM,eAAe,YAAY,mBAC1C,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC9C,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC;gDACC,WAAU;gDACV,OAAO,EAAA,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,WAAW,KAAI;gDACzC,UAAU,CAAC,IAAM,eAAe,YAAY,eAAe,EAAE,MAAM,CAAC,KAAK;;kEAEzE,6LAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,EAAA,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,UAAU,KAAI;gDACxC,UAAU,CAAC,IAAM,eAAe,YAAY,cAC1C,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC9C,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,6LAAC;wCACC,WAAU;wCACV,OAAO,EAAA,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,gBAAgB,KAAI;wCAC9C,UAAU,CAAC,IAAM,eAAe,YAAY,oBAAoB,EAAE,MAAM,CAAC,KAAK;;0DAE9E,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,EAAA,qBAAA,SAAS,QAAQ,cAAjB,yCAAA,mBAAmB,gBAAgB,KAAI;wCAChD,UAAU,CAAC,IAAM,eAAe,YAAY,oBAAoB,EAAE,MAAM,CAAC,OAAO;wCAChF,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAmB,WAAU;kDAAsB;;;;;;;;;;;;0CAKpE,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,EAAA,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,UAAU,KAAI;wCACxC,UAAU,CAAC,IAAM,eAAe,YAAY,cAC1C,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;wCAC9C,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAOpB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAClD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAO,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,YAAY,KAAI;oCACtC,UAAU,CAAC,IAAM,eAAe,QAAQ,gBACtC,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;oCAC9C,aAAY;;;;;;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,WAAW,cAAc;;;;;;;;;;;;;;;;;AAKpC;GAvLgB;KAAA", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/components/settings/device-list.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Device } from '@/types/sms-gateway';\nimport { formatDateTime, formatRelativeTime } from '@/lib/utils';\nimport { Trash2 } from 'lucide-react';\n\nexport function DeviceList() {\n  const [devices, setDevices] = useState<Device[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [deletingDevice, setDeletingDevice] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  const fetchDevices = async () => {\n    try {\n      const response = await fetch('/api/devices');\n      if (response.ok) {\n        const data = await response.json();\n        setDevices(data);\n      }\n    } catch (error) {\n      console.error('Error fetching devices:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteDevice = async (deviceId: string) => {\n    if (!confirm('Are you sure you want to delete this device?')) {\n      return;\n    }\n\n    setDeletingDevice(deviceId);\n    try {\n      const response = await fetch(`/api/devices/${deviceId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setDevices(devices.filter(device => device.id !== deviceId));\n      } else {\n        alert('Failed to delete device');\n      }\n    } catch (error) {\n      console.error('Error deleting device:', error);\n      alert('Failed to delete device');\n    } finally {\n      setDeletingDevice(null);\n    }\n  };\n\n  if (loading) {\n    return <div className=\"flex justify-center p-8\">Loading devices...</div>;\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Connected Devices</CardTitle>\n        <CardDescription>Manage your SMS gateway devices</CardDescription>\n      </CardHeader>\n      <CardContent>\n        {devices.length === 0 ? (\n          <div className=\"text-center py-8 text-muted-foreground\">\n            No devices connected\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {devices.map((device) => (\n              <div\n                key={device.id}\n                className=\"flex items-center justify-between p-4 border rounded-lg\"\n              >\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div>\n                      <h3 className=\"font-medium\">{device.name}</h3>\n                      <p className=\"text-sm text-muted-foreground\">\n                        ID: {device.id}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"mt-2 text-sm text-muted-foreground\">\n                    <div className=\"flex space-x-4\">\n                      <span>\n                        Created: {formatDateTime(device.createdAt)}\n                      </span>\n                      <span>\n                        Last seen: {formatRelativeTime(device.lastSeen)}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"flex items-center\">\n                    <div\n                      className={`w-2 h-2 rounded-full mr-2 ${\n                        new Date(device.lastSeen).getTime() > Date.now() - 5 * 60 * 1000\n                          ? 'bg-green-500'\n                          : 'bg-red-500'\n                      }`}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">\n                      {new Date(device.lastSeen).getTime() > Date.now() - 5 * 60 * 1000\n                        ? 'Online'\n                        : 'Offline'}\n                    </span>\n                  </div>\n                  \n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handleDeleteDevice(device.id)}\n                    disabled={deletingDevice === device.id}\n                    className=\"text-red-600 hover:text-red-700\"\n                  >\n                    {deletingDevice === device.id ? (\n                      'Deleting...'\n                    ) : (\n                      <Trash2 className=\"h-4 w-4\" />\n                    )}\n                  </Button>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,iDAAiD;YAC5D;QACF;QAEA,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAwB,OAAT,WAAY;gBACvD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YACpD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC;YAAI,WAAU;sBAA0B;;;;;;IAClD;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,6LAAC,mIAAA,CAAA,cAAW;0BACT,QAAQ,MAAM,KAAK,kBAClB,6LAAC;oBAAI,WAAU;8BAAyC;;;;;yCAIxD,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAe,OAAO,IAAI;;;;;;kEACxC,6LAAC;wDAAE,WAAU;;4DAAgC;4DACtC,OAAO,EAAE;;;;;;;;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DACM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS;;;;;;;kEAE3C,6LAAC;;4DAAK;4DACQ,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAMtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAW,AAAC,6BAIX,OAHC,IAAI,KAAK,OAAO,QAAQ,EAAE,OAAO,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,OACxD,iBACA;;;;;;8DAGR,6LAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,OAAO,QAAQ,EAAE,OAAO,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,OACzD,WACA;;;;;;;;;;;;sDAIR,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;4CAC3C,UAAU,mBAAmB,OAAO,EAAE;4CACtC,WAAU;sDAET,mBAAmB,OAAO,EAAE,GAC3B,8BAEA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BAlDnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;AA6D9B;GA/HgB;KAAA", "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DeviceSettingsForm } from '@/components/settings/device-settings-form';\nimport { DeviceList } from '@/components/settings/device-list';\nimport { DeviceSettings } from '@/types/sms-gateway';\n\nexport default function SettingsPage() {\n  const [saving, setSaving] = useState(false);\n\n  const handleSaveSettings = async (settings: DeviceSettings) => {\n    setSaving(true);\n    try {\n      const response = await fetch('/api/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(settings),\n      });\n\n      if (response.ok) {\n        alert('Settings saved successfully!');\n      } else {\n        alert('Failed to save settings');\n      }\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      alert('Failed to save settings');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto py-8 px-4\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold\">Settings</h1>\n        <p className=\"text-muted-foreground mt-2\">\n          Manage your SMS gateway device settings and connected devices\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        <div className=\"lg:col-span-2\">\n          <DeviceSettingsForm onSave={handleSaveSettings} loading={saving} />\n        </div>\n        \n        <div className=\"lg:col-span-1\">\n          <DeviceList />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,qBAAqB,OAAO;QAChC,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,qBAAkB;4BAAC,QAAQ;4BAAoB,SAAS;;;;;;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;AAKrB;GA/CwB;KAAA", "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}