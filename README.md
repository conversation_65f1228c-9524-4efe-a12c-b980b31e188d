# SMS Gateway - Android Integration

A comprehensive Next.js application that integrates with the Android SMS Gateway service using the `android-sms-gateway` npm package. This application provides a complete web interface for managing SMS operations through your Android devices.

## Features

- **Device Settings Management** - Configure and manage SMS gateway device settings
- **Message Sending** - Send SMS messages through the gateway with a user-friendly interface
- **Status Tracking** - Real-time status monitoring for sent messages to track delivery status
- **Message Reading** - Retrieve and display incoming SMS messages from the gateway
- **Webhook Integration** - Set up webhook endpoints to receive real-time notifications
- **LINE Bot Integration** - Forward received SMS messages to LINE for instant notifications
- **Dashboard** - Comprehensive overview of your SMS gateway operations

## Tech Stack

- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **android-sms-gateway** npm package for API integration
- **Lucide React** for icons

## Getting Started

### Prerequisites

- Node.js 18+ installed
- An Android device with SMS Gateway app installed
- SMS Gateway credentials (login/password)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd sms-gateway-app
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Edit `.env.local` with your SMS Gateway credentials:
```env
ANDROID_SMS_GATEWAY_LOGIN=your_username
ANDROID_SMS_GATEWAY_PASSWORD=your_password
ANDROID_SMS_GATEWAY_BASE_URL=https://api.sms-gate.app/3rdparty/v1
WEBHOOK_SECRET=your_webhook_secret_here
```

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Configuration

### Environment Variables

- `ANDROID_SMS_GATEWAY_LOGIN` - Your SMS Gateway username
- `ANDROID_SMS_GATEWAY_PASSWORD` - Your SMS Gateway password
- `ANDROID_SMS_GATEWAY_BASE_URL` - API base URL (default: https://api.sms-gate.app/3rdparty/v1)
- `WEBHOOK_SECRET` - Secret for webhook signature verification (optional)
- `LINE_CHANNEL_ACCESS_TOKEN` - LINE Bot channel access token
- `LINE_CHANNEL_SECRET` - LINE Bot channel secret
- `LINE_USER_ID` - Your LINE user ID for receiving notifications

### SMS Gateway Setup

1. Install the SMS Gateway for Android app on your device
2. Configure the app with your server settings
3. Note your login credentials for the environment variables

### LINE Bot Setup

1. Create a LINE Bot in the [LINE Developers Console](https://developers.line.biz/)
2. Create a new channel (Messaging API)
3. Get your Channel Access Token from the channel settings
4. Get your Channel Secret from the channel basic settings
5. Add the bot as a friend and get your User ID
6. Configure the LINE environment variables in `.env.local`
7. Test the integration using the LINE Bot page in the application

## Usage

### Dashboard
- Overview of connected devices and system status
- Quick access to all features
- Real-time statistics

### Send Messages
- Send SMS to single or multiple recipients
- Configure message priority and TTL
- Support for different SIM cards
- Real-time delivery tracking

### Status Tracking
- Monitor message delivery status
- Search and filter messages
- Real-time status updates
- Delivery statistics

### Inbox
- View received SMS messages
- Export message history
- Filter by sender or date range

### Webhooks
- Configure webhook endpoints
- Real-time notifications for SMS events
- Webhook testing tools
- Signature verification support

### Settings
- Manage device settings
- Configure message limits and intervals
- Webhook and logging settings
- Device management

## API Endpoints

The application provides the following API endpoints:

- `GET /api/devices` - List connected devices
- `DELETE /api/devices/[id]` - Remove a device
- `GET /api/settings` - Get device settings
- `PUT /api/settings` - Update device settings
- `PATCH /api/settings` - Partially update settings
- `POST /api/messages` - Send SMS message
- `GET /api/messages/[id]` - Get message status
- `GET /api/webhooks` - List webhooks
- `POST /api/webhooks` - Create webhook
- `DELETE /api/webhooks/[id]` - Delete webhook
- `POST /api/webhooks/receive` - Webhook receiver endpoint
- `POST /api/inbox/export` - Export inbox messages
- `GET /api/health` - Health check

## Development

### Project Structure

```
src/
├── app/                 # Next.js app router pages
├── components/          # React components
│   ├── ui/             # Base UI components
│   ├── dashboard/      # Dashboard components
│   ├── messages/       # Message-related components
│   ├── settings/       # Settings components
│   ├── webhooks/       # Webhook components
│   └── layout/         # Layout components
├── lib/                # Utility libraries
├── hooks/              # Custom React hooks
└── types/              # TypeScript type definitions
```

### Key Components

- **Navigation** - Sidebar navigation component
- **DashboardOverview** - Main dashboard with statistics
- **SendMessageForm** - Form for sending SMS messages
- **MessageStatus** - Display message delivery status
- **DeviceSettingsForm** - Configure device settings
- **WebhookForm** - Create and manage webhooks

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the [SMS Gateway documentation](https://docs.sms-gate.app/)
- Review the [android-sms-gateway package](https://www.npmjs.com/package/android-sms-gateway)
- Open an issue in this repository
