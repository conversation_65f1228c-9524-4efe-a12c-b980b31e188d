'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Device, WebHookEventType, RegisterWebHookRequest, WebHook } from '@/types/sms-gateway';
import { Plus } from 'lucide-react';

interface WebhookFormProps {
  onWebhookCreated: (webhook: WebHook) => void;
}

export function WebhookForm({ onWebhookCreated }: WebhookFormProps) {
  const [devices, setDevices] = useState<Device[]>([]);
  const [url, setUrl] = useState('');
  const [event, setEvent] = useState<WebHookEventType>(WebHookEventType.SmsReceived);
  const [deviceId, setDeviceId] = useState('');
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDevices();
    
    // Set default webhook URL to the receive endpoint
    const baseUrl = window.location.origin;
    setUrl(`${baseUrl}/api/webhooks/receive`);
  }, []);

  const fetchDevices = async () => {
    try {
      const response = await fetch('/api/devices');
      if (response.ok) {
        const data = await response.json();
        setDevices(data);
        if (data.length > 0) {
          setDeviceId(data[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching devices:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url || !event) {
      setError('URL and event type are required');
      return;
    }

    // Validate URL
    try {
      new URL(url);
    } catch {
      setError('Please enter a valid URL');
      return;
    }

    setCreating(true);
    setError(null);

    try {
      const webhookRequest: RegisterWebHookRequest = {
        url,
        event,
        ...(deviceId && { deviceId })
      };

      const response = await fetch('/api/webhooks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookRequest),
      });

      if (response.ok) {
        const webhook = await response.json();
        onWebhookCreated(webhook);
        
        // Reset form
        setUrl(`${window.location.origin}/api/webhooks/receive`);
        setEvent(WebHookEventType.SmsReceived);
        if (devices.length > 0) {
          setDeviceId(devices[0].id);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create webhook');
      }
    } catch (error) {
      console.error('Error creating webhook:', error);
      setError('Failed to create webhook');
    } finally {
      setCreating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Plus className="h-5 w-5" />
          <span>Create Webhook</span>
        </CardTitle>
        <CardDescription>
          Register a webhook to receive real-time notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* URL */}
          <div>
            <label className="block text-sm font-medium mb-1">Webhook URL</label>
            <Input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://your-app.com/webhook"
              required
            />
            <p className="text-xs text-muted-foreground mt-1">
              The URL where webhook notifications will be sent
            </p>
          </div>

          {/* Event Type */}
          <div>
            <label className="block text-sm font-medium mb-1">Event Type</label>
            <select
              value={event}
              onChange={(e) => setEvent(e.target.value as WebHookEventType)}
              className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
              required
            >
              <option value={WebHookEventType.SmsReceived}>SMS Received</option>
              <option value={WebHookEventType.SmsStatusChanged}>SMS Status Changed</option>
            </select>
            <p className="text-xs text-muted-foreground mt-1">
              {event === WebHookEventType.SmsReceived
                ? 'Triggered when a new SMS is received (sms:received)'
                : 'Triggered when SMS delivery status changes (sms:status-changed)'
              }
            </p>
          </div>

          {/* Device Selection */}
          <div>
            <label className="block text-sm font-medium mb-1">Device (Optional)</label>
            <select
              value={deviceId}
              onChange={(e) => setDeviceId(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="">All devices</option>
              {devices.map((device) => (
                <option key={device.id} value={device.id}>
                  {device.name} ({device.id})
                </option>
              ))}
            </select>
            <p className="text-xs text-muted-foreground mt-1">
              Leave empty to receive notifications from all devices
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={creating}
            className="w-full"
          >
            {creating ? 'Creating...' : 'Create Webhook'}
          </Button>
        </form>

        {/* Info Box */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Webhook Security</h4>
          <p className="text-xs text-blue-700">
            Webhooks can be secured with HMAC signatures. Set the WEBHOOK_SECRET environment 
            variable to enable signature verification. The signature will be sent in the 
            'x-signature' header as 'sha256=&lt;hash&gt;'.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
