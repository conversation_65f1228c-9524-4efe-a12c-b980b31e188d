import { NextRequest, NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';

export async function POST(request: NextRequest) {
  try {
    const { deviceId, since, until } = await request.json();
    
    if (!deviceId || !since || !until) {
      return NextResponse.json(
        { error: 'deviceId, since, and until are required' },
        { status: 400 }
      );
    }

    const smsService = getSmsGatewayService();
    
    await smsService.exportInbox({
      deviceId,
      since,
      until
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error exporting inbox:', error);
    return NextResponse.json(
      { error: 'Failed to export inbox' },
      { status: 500 }
    );
  }
}
