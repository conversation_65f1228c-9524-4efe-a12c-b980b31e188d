import { NextRequest, NextResponse } from 'next/server';
import { getSmsGatewayService } from '@/lib/sms-gateway-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    const { messageId } = params;
    const smsService = getSmsGatewayService();
    
    const messageState = await smsService.getMessageState(messageId);
    
    return NextResponse.json(messageState);
  } catch (error) {
    console.error('Error fetching message state:', error);
    return NextResponse.json(
      { error: 'Failed to fetch message state' },
      { status: 500 }
    );
  }
}
