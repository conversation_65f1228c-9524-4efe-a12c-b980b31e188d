{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/lib/http-client.ts"], "sourcesContent": ["import { HttpClient } from '@/types/sms-gateway';\n\nexport class FetchHttpClient implements HttpClient {\n  async get<T>(url: string, headers?: Record<string, string>): Promise<T> {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    return response.json();\n  }\n\n  async post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      body: JSON.stringify(body),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    return response.json();\n  }\n\n  async put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {\n    const response = await fetch(url, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      body: JSON.stringify(body),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    return response.json();\n  }\n\n  async patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {\n    const response = await fetch(url, {\n      method: 'PATCH',\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      body: JSON.stringify(body),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    return response.json();\n  }\n\n  async delete<T>(url: string, headers?: Record<string, string>): Promise<T> {\n    const response = await fetch(url, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    // Handle empty responses for DELETE operations\n    const text = await response.text();\n    return text ? JSON.parse(text) : ({} as T);\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,MAAM,IAAO,GAAW,EAAE,OAAgC,EAAc;QACtE,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAO;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,KAAQ,GAAW,EAAE,IAAS,EAAE,OAAgC,EAAc;QAClF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAO;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAAS,EAAE,OAAgC,EAAc;QACjF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAO;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAS,GAAW,EAAE,IAAS,EAAE,OAAgC,EAAc;QACnF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAO;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAE,OAAgC,EAAc;QACzE,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAO;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,+CAA+C;QAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAS,CAAC;IACrC;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/lib/sms-gateway-service.ts"], "sourcesContent": ["import Client from 'android-sms-gateway';\nimport { FetchHttpClient } from './http-client';\nimport {\n  MessageRequest,\n  MessageState,\n  WebHook,\n  RegisterWebHookRequest,\n  Device,\n  DeviceSettings,\n  HealthResponse,\n  LogEntry,\n  MessagesExportRequest,\n} from '@/types/sms-gateway';\n\nexport class SmsGatewayService {\n  private client: Client;\n\n  constructor(\n    login: string,\n    password: string,\n    baseUrl: string = 'https://api.sms-gate.app/3rdparty/v1'\n  ) {\n    const httpClient = new FetchHttpClient();\n    this.client = new Client(login, password, httpClient, baseUrl);\n  }\n\n  // Message operations\n  async sendMessage(\n    message: MessageRequest,\n    options?: { skipPhoneValidation?: boolean }\n  ): Promise<MessageState> {\n    return this.client.send(message, options);\n  }\n\n  async getMessageState(messageId: string): Promise<MessageState> {\n    return this.client.getState(messageId);\n  }\n\n  // Webhook operations\n  async getWebhooks(): Promise<WebHook[]> {\n    return this.client.getWebhooks();\n  }\n\n  async registerWebhook(request: RegisterWebHookRequest): Promise<WebHook> {\n    return this.client.registerWebhook(request);\n  }\n\n  async deleteWebhook(webhookId: string): Promise<void> {\n    return this.client.deleteWebhook(webhookId);\n  }\n\n  // Device operations\n  async getDevices(): Promise<Device[]> {\n    return this.client.getDevices();\n  }\n\n  async deleteDevice(deviceId: string): Promise<void> {\n    return this.client.deleteDevice(deviceId);\n  }\n\n  // Health check\n  async getHealth(): Promise<HealthResponse> {\n    return this.client.getHealth();\n  }\n\n  // Inbox export\n  async exportInbox(request: MessagesExportRequest): Promise<void> {\n    return this.client.exportInbox(request);\n  }\n\n  // Logs\n  async getLogs(from?: Date, to?: Date): Promise<LogEntry[]> {\n    return this.client.getLogs(from, to);\n  }\n\n  // Settings operations\n  async getSettings(): Promise<DeviceSettings> {\n    return this.client.getSettings();\n  }\n\n  async updateSettings(settings: DeviceSettings): Promise<void> {\n    return this.client.updateSettings(settings);\n  }\n\n  async patchSettings(settings: Partial<DeviceSettings>): Promise<void> {\n    return this.client.patchSettings(settings);\n  }\n}\n\n// Singleton instance\nlet smsGatewayService: SmsGatewayService | null = null;\n\nexport function getSmsGatewayService(): SmsGatewayService {\n  if (!smsGatewayService) {\n    const login = process.env.ANDROID_SMS_GATEWAY_LOGIN;\n    const password = process.env.ANDROID_SMS_GATEWAY_PASSWORD;\n    const baseUrl = process.env.ANDROID_SMS_GATEWAY_BASE_URL;\n\n    if (!login || !password) {\n      throw new Error('SMS Gateway credentials not configured');\n    }\n\n    smsGatewayService = new SmsGatewayService(login, password, baseUrl);\n  }\n\n  return smsGatewayService;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAaO,MAAM;IACH,OAAe;IAEvB,YACE,KAAa,EACb,QAAgB,EAChB,UAAkB,sCAAsC,CACxD;QACA,MAAM,aAAa,IAAI,8HAAA,CAAA,kBAAe;QACtC,IAAI,CAAC,MAAM,GAAG,IAAI,4JAAA,CAAA,UAAM,CAAC,OAAO,UAAU,YAAY;IACxD;IAEA,qBAAqB;IACrB,MAAM,YACJ,OAAuB,EACvB,OAA2C,EACpB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;IACnC;IAEA,MAAM,gBAAgB,SAAiB,EAAyB;QAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B;IAEA,qBAAqB;IACrB,MAAM,cAAkC;QACtC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;IAChC;IAEA,MAAM,gBAAgB,OAA+B,EAAoB;QACvE,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACrC;IAEA,MAAM,cAAc,SAAiB,EAAiB;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IACnC;IAEA,oBAAoB;IACpB,MAAM,aAAgC;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU;IAC/B;IAEA,MAAM,aAAa,QAAgB,EAAiB;QAClD,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAClC;IAEA,eAAe;IACf,MAAM,YAAqC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;IAC9B;IAEA,eAAe;IACf,MAAM,YAAY,OAA8B,EAAiB;QAC/D,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACjC;IAEA,OAAO;IACP,MAAM,QAAQ,IAAW,EAAE,EAAS,EAAuB;QACzD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;IACnC;IAEA,sBAAsB;IACtB,MAAM,cAAuC;QAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;IAChC;IAEA,MAAM,eAAe,QAAwB,EAAiB;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;IACpC;IAEA,MAAM,cAAc,QAAiC,EAAiB;QACpE,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IACnC;AACF;AAEA,qBAAqB;AACrB,IAAI,oBAA8C;AAE3C,SAAS;IACd,IAAI,CAAC,mBAAmB;QACtB,MAAM,QAAQ,QAAQ,GAAG,CAAC,yBAAyB;QACnD,MAAM,WAAW,QAAQ,GAAG,CAAC,4BAA4B;QACzD,MAAM,UAAU,QAAQ,GAAG,CAAC,4BAA4B;QAExD,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,oBAAoB,IAAI,kBAAkB,OAAO,UAAU;IAC7D;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/sms/src/app/api/settings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getSmsGatewayService } from '@/lib/sms-gateway-service';\n\nexport async function GET() {\n  try {\n    const smsService = getSmsGatewayService();\n    const settings = await smsService.getSettings();\n    \n    return NextResponse.json(settings);\n  } catch (error) {\n    console.error('Error fetching settings:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch settings' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PUT(request: NextRequest) {\n  try {\n    const settings = await request.json();\n    const smsService = getSmsGatewayService();\n    \n    await smsService.updateSettings(settings);\n    \n    return NextResponse.json({ success: true });\n  } catch (error) {\n    console.error('Error updating settings:', error);\n    return NextResponse.json(\n      { error: 'Failed to update settings' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    const partialSettings = await request.json();\n    const smsService = getSmsGatewayService();\n    \n    await smsService.patchSettings(partialSettings);\n    \n    return NextResponse.json({ success: true });\n  } catch (error) {\n    console.error('Error patching settings:', error);\n    return NextResponse.json(\n      { error: 'Failed to patch settings' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,aAAa,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD;QACtC,MAAM,WAAW,MAAM,WAAW,WAAW;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,IAAI;QACnC,MAAM,aAAa,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD;QAEtC,MAAM,WAAW,cAAc,CAAC;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,MAAM,OAAoB;IAC9C,IAAI;QACF,MAAM,kBAAkB,MAAM,QAAQ,IAAI;QAC1C,MAAM,aAAa,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD;QAEtC,MAAM,WAAW,aAAa,CAAC;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}