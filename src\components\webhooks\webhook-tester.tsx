'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { WebHookEventType } from '@/types/sms-gateway';
import { TestTube, Play } from 'lucide-react';

export function WebhookTester() {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<string | null>(null);

  const testWebhookEndpoint = async () => {
    setTesting(true);
    setTestResults(null);

    try {
      // Test SMS Received webhook
      const smsReceivedPayload = {
        event: WebHookEventType.SmsReceived,
        deviceId: 'test-device-123',
        message: {
          id: 'test-msg-' + Date.now(),
          phoneNumber: '+1234567890',
          text: 'This is a test message from webhook tester',
          receivedAt: new Date().toISOString()
        }
      };

      const response = await fetch('/api/webhooks/receive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(smsReceivedPayload),
      });

      if (response.ok) {
        setTestResults('✅ Webhook endpoint is working correctly!');
      } else {
        const errorText = await response.text();
        setTestResults(`❌ Webhook test failed: ${response.status} ${response.statusText}\n${errorText}`);
      }
    } catch (error) {
      console.error('Error testing webhook:', error);
      setTestResults(`❌ Webhook test failed: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  const testStatusChangeWebhook = async () => {
    setTesting(true);
    setTestResults(null);

    try {
      // Test SMS Status Changed webhook
      const statusChangePayload = {
        event: WebHookEventType.SmsStatusChanged,
        deviceId: 'test-device-123',
        message: {
          id: 'test-msg-' + Date.now(),
          state: 'Delivered',
          recipients: [
            {
              phoneNumber: '+1234567890',
              state: 'Delivered'
            }
          ]
        }
      };

      const response = await fetch('/api/webhooks/receive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(statusChangePayload),
      });

      if (response.ok) {
        setTestResults('✅ Status change webhook endpoint is working correctly!');
      } else {
        const errorText = await response.text();
        setTestResults(`❌ Status change webhook test failed: ${response.status} ${response.statusText}\n${errorText}`);
      }
    } catch (error) {
      console.error('Error testing status change webhook:', error);
      setTestResults(`❌ Status change webhook test failed: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TestTube className="h-5 w-5" />
          <span>Webhook Tester</span>
        </CardTitle>
        <CardDescription>
          Test your webhook endpoint to ensure it's working correctly
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button
            onClick={testWebhookEndpoint}
            disabled={testing}
            variant="outline"
            className="h-auto p-4 flex flex-col items-center space-y-2"
          >
            <Play className="h-5 w-5" />
            <span>Test SMS Received</span>
            <span className="text-xs text-muted-foreground">
              Simulates receiving an SMS
            </span>
          </Button>

          <Button
            onClick={testStatusChangeWebhook}
            disabled={testing}
            variant="outline"
            className="h-auto p-4 flex flex-col items-center space-y-2"
          >
            <Play className="h-5 w-5" />
            <span>Test Status Change</span>
            <span className="text-xs text-muted-foreground">
              Simulates status update
            </span>
          </Button>
        </div>

        {testing && (
          <div className="text-center py-4">
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span>Testing webhook endpoint...</span>
            </div>
          </div>
        )}

        {testResults && (
          <div className="p-4 bg-muted rounded-md">
            <h4 className="font-medium mb-2">Test Results:</h4>
            <pre className="text-sm whitespace-pre-wrap">{testResults}</pre>
          </div>
        )}

        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Webhook Endpoint</h4>
          <code className="text-sm text-blue-700 bg-blue-100 px-2 py-1 rounded">
            {typeof window !== 'undefined' ? `${window.location.origin}/api/webhooks/receive` : '/api/webhooks/receive'}
          </code>
          <p className="text-xs text-blue-700 mt-2">
            Use this URL when configuring webhooks in your SMS Gateway settings.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
