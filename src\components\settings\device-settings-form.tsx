'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DeviceSettings } from '@/types/sms-gateway';

interface DeviceSettingsFormProps {
  onSave: (settings: DeviceSettings) => Promise<void>;
  loading?: boolean;
}

export function DeviceSettingsForm({ onSave, loading = false }: DeviceSettingsFormProps) {
  const [settings, setSettings] = useState<DeviceSettings>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave(settings);
    } finally {
      setIsSaving(false);
    }
  };

  const updateSettings = (section: keyof DeviceSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Messages Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Message Settings</CardTitle>
          <CardDescription>Configure message sending behavior</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Send Interval Min (ms)</label>
              <Input
                type="number"
                value={settings.messages?.sendIntervalMin || ''}
                onChange={(e) => updateSettings('messages', 'sendIntervalMin', 
                  e.target.value ? parseInt(e.target.value) : null)}
                placeholder="Minimum interval"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Send Interval Max (ms)</label>
              <Input
                type="number"
                value={settings.messages?.sendIntervalMax || ''}
                onChange={(e) => updateSettings('messages', 'sendIntervalMax', 
                  e.target.value ? parseInt(e.target.value) : null)}
                placeholder="Maximum interval"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Limit Period</label>
              <select
                className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
                value={settings.messages?.limitPeriod || 'Disabled'}
                onChange={(e) => updateSettings('messages', 'limitPeriod', e.target.value)}
              >
                <option value="Disabled">Disabled</option>
                <option value="PerMinute">Per Minute</option>
                <option value="PerHour">Per Hour</option>
                <option value="PerDay">Per Day</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Limit Value</label>
              <Input
                type="number"
                value={settings.messages?.limitValue || ''}
                onChange={(e) => updateSettings('messages', 'limitValue', 
                  e.target.value ? parseInt(e.target.value) : null)}
                placeholder="Message limit"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">SIM Selection Mode</label>
            <select
              className="w-full h-10 px-3 py-2 border border-input rounded-md bg-background"
              value={settings.messages?.simSelectionMode || 'OSDefault'}
              onChange={(e) => updateSettings('messages', 'simSelectionMode', e.target.value)}
            >
              <option value="OSDefault">OS Default</option>
              <option value="RoundRobin">Round Robin</option>
              <option value="Random">Random</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Webhook Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Webhook Settings</CardTitle>
          <CardDescription>Configure webhook behavior</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="internetRequired"
              checked={settings.webhooks?.internetRequired || false}
              onChange={(e) => updateSettings('webhooks', 'internetRequired', e.target.checked)}
              className="rounded border-input"
            />
            <label htmlFor="internetRequired" className="text-sm font-medium">
              Internet Required
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Retry Count</label>
            <Input
              type="number"
              value={settings.webhooks?.retryCount || ''}
              onChange={(e) => updateSettings('webhooks', 'retryCount', 
                e.target.value ? parseInt(e.target.value) : 1)}
              placeholder="Number of retries"
            />
          </div>
        </CardContent>
      </Card>

      {/* Logs Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Log Settings</CardTitle>
          <CardDescription>Configure log retention</CardDescription>
        </CardHeader>
        <CardContent>
          <div>
            <label className="block text-sm font-medium mb-1">Log Lifetime (days)</label>
            <Input
              type="number"
              value={settings.logs?.lifetimeDays || ''}
              onChange={(e) => updateSettings('logs', 'lifetimeDays', 
                e.target.value ? parseInt(e.target.value) : 30)}
              placeholder="Days to keep logs"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button 
          onClick={handleSave} 
          disabled={isSaving || loading}
          className="min-w-[120px]"
        >
          {isSaving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>
    </div>
  );
}
