'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { WebHook, WebHookEventType } from '@/types/sms-gateway';
import { Trash2, ExternalLink, Webhook } from 'lucide-react';

interface WebhookListProps {
  webhooks: WebHook[];
  onWebhookDeleted: (webhookId: string) => void;
  onRefresh: () => void;
}

export function WebhookList({ webhooks, onWebhookDeleted, onRefresh }: WebhookListProps) {
  const [deletingWebhook, setDeletingWebhook] = useState<string | null>(null);

  const handleDeleteWebhook = async (webhookId: string) => {
    if (!confirm('Are you sure you want to delete this webhook?')) {
      return;
    }

    setDeletingWebhook(webhookId);
    try {
      const response = await fetch(`/api/webhooks/${webhookId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onWebhookDeleted(webhookId);
      } else {
        alert('Failed to delete webhook');
      }
    } catch (error) {
      console.error('Error deleting webhook:', error);
      alert('Failed to delete webhook');
    } finally {
      setDeletingWebhook(null);
    }
  };

  const getEventTypeColor = (eventType: WebHookEventType) => {
    switch (eventType) {
      case WebHookEventType.SmsReceived:
        return 'bg-green-100 text-green-800';
      case WebHookEventType.SmsStatusChanged:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEventTypeDescription = (eventType: WebHookEventType) => {
    switch (eventType) {
      case WebHookEventType.SmsReceived:
        return 'Notifies when new SMS messages are received (sms:received)';
      case WebHookEventType.SmsStatusChanged:
        return 'Notifies when SMS delivery status changes (sms:status-changed)';
      default:
        return 'Unknown event type';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Webhook className="h-5 w-5" />
              <span>Active Webhooks</span>
            </CardTitle>
            <CardDescription>Manage your webhook subscriptions</CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {webhooks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No webhooks configured
          </div>
        ) : (
          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <div
                key={webhook.id}
                className="p-4 border rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(webhook.event)}`}>
                        {webhook.event}
                      </span>
                      {webhook.deviceId && (
                        <span className="text-xs text-muted-foreground px-2 py-1 bg-muted rounded">
                          Device: {webhook.deviceId}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <code className="text-sm bg-muted px-2 py-1 rounded font-mono">
                        {webhook.url}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(webhook.url, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <p className="text-sm text-muted-foreground">
                      {getEventTypeDescription(webhook.event)}
                    </p>
                    
                    <div className="mt-2 text-xs text-muted-foreground">
                      <span>ID: {webhook.id}</span>
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteWebhook(webhook.id)}
                    disabled={deletingWebhook === webhook.id}
                    className="text-red-600 hover:text-red-700"
                  >
                    {deletingWebhook === webhook.id ? (
                      'Deleting...'
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
