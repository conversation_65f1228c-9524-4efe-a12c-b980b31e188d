'use client';

import { useState, useEffect } from 'react';
import { WebhookForm } from '@/components/webhooks/webhook-form';
import { WebhookList } from '@/components/webhooks/webhook-list';
import { WebhookTester } from '@/components/webhooks/webhook-tester';
import { WebHook } from '@/types/sms-gateway';

export default function WebhooksPage() {
  const [webhooks, setWebhooks] = useState<WebHook[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchWebhooks();
  }, []);

  const fetchWebhooks = async () => {
    try {
      const response = await fetch('/api/webhooks');
      if (response.ok) {
        const data = await response.json();
        setWebhooks(data);
      }
    } catch (error) {
      console.error('Error fetching webhooks:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleWebhookCreated = (webhook: WebHook) => {
    setWebhooks(prev => [webhook, ...prev]);
  };

  const handleWebhookDeleted = (webhookId: string) => {
    setWebhooks(prev => prev.filter(webhook => webhook.id !== webhookId));
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Webhook Integration</h1>
        <p className="text-muted-foreground mt-2">
          Set up webhooks to receive real-time notifications from your SMS Gateway
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <WebhookForm onWebhookCreated={handleWebhookCreated} />
          <WebhookTester />
        </div>
        
        <div>
          <WebhookList
            webhooks={webhooks}
            onWebhookDeleted={handleWebhookDeleted}
            onRefresh={fetchWebhooks}
          />
        </div>
      </div>
    </div>
  );
}
