'use client';

import { useState } from 'react';
import { DeviceSettingsForm } from '@/components/settings/device-settings-form';
import { DeviceList } from '@/components/settings/device-list';
import { DeviceSettings } from '@/types/sms-gateway';

export default function SettingsPage() {
  const [saving, setSaving] = useState(false);

  const handleSaveSettings = async (settings: DeviceSettings) => {
    setSaving(true);
    try {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        alert('Settings saved successfully!');
      } else {
        alert('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground mt-2">
          Manage your SMS gateway device settings and connected devices
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <DeviceSettingsForm onSave={handleSaveSettings} loading={saving} />
        </div>
        
        <div className="lg:col-span-1">
          <DeviceList />
        </div>
      </div>
    </div>
  );
}
